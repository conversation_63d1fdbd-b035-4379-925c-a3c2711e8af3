<?php

namespace App\Core\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Response;

class CoreServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register core services here
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register response macros
        $this->registerResponseMacros();
    }

    /**
     * Register response macros
     */
    protected function registerResponseMacros(): void
    {
        Response::macro('success', function ($data = null, $message = 'Success', $status = 200) {
            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => $data,
                'timestamp' => now()->toISOString(),
            ], $status);
        });

        Response::macro('error', function ($message = 'Error occurred', $status = 400, $errors = []) {
            $response = [
                'success' => false,
                'message' => $message,
                'timestamp' => now()->toISOString(),
            ];

            if (!empty($errors)) {
                $response['errors'] = $errors;
            }

            return response()->json($response, $status);
        });

        Response::macro('paginated', function ($data, $pagination, $message = 'Success') {
            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => $data,
                'pagination' => $pagination,
                'timestamp' => now()->toISOString(),
            ]);
        });
    }
}
