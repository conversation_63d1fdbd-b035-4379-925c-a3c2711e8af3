<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        if (!Auth::check()) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        $user = Auth::user();
        $user = User::with('role')->find($user->id);

        if (!$user->role) {
            return response()->json(['message' => 'User has no role assigned'], 403);
        }

        if (!in_array($user->role->name, $roles)) {
            return response()->json([
                'message' => 'Access denied. Required roles: ' . implode(', ', $roles)
            ], 403);
        }

        return $next($request);
    }
}
