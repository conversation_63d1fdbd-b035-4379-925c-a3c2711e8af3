<?php

namespace App\Modules\Auth\Http\Requests;

use App\Core\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class LoginRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'login' => [
                'required',
                'string',
                'max:255'
            ],
            'password' => [
                'required',
                'string',
                'min:6'
            ],
            'remember' => [
                'nullable',
                'boolean'
            ],
            'device_name' => [
                'nullable',
                'string',
                'max:255'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'login.required' => 'Email atau NIS wajib diisi.',
            'login.string' => 'Email atau NIS harus berupa teks.',
            'login.max' => 'Email atau NIS maksimal 255 karakter.',
            'password.required' => 'Password wajib diisi.',
            'password.string' => 'Password harus berupa teks.',
            'password.min' => 'Password minimal 6 karakter.',
            'remember.boolean' => 'Remember me harus berupa boolean.',
            'device_name.string' => 'Nama perangkat harus berupa teks.',
            'device_name.max' => 'Nama perangkat maksimal 255 karakter.'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'login' => 'email atau NIS',
            'password' => 'password',
            'remember' => 'ingat saya',
            'device_name' => 'nama perangkat'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'login' => strtolower(trim($this->login)),
            'remember' => filter_var($this->remember, FILTER_VALIDATE_BOOLEAN),
            'device_name' => $this->device_name ?? $this->userAgent()
        ]);
    }

    /**
     * Get the login credentials from the request.
     */
    public function getCredentials(): array
    {
        $login = $this->input('login');
        
        // Determine if login is email or student_id
        $field = filter_var($login, FILTER_VALIDATE_EMAIL) ? 'email' : 'student_id';
        
        return [
            $field => $login,
            'password' => $this->input('password')
        ];
    }

    /**
     * Check if the login should be remembered.
     */
    public function shouldRemember(): bool
    {
        return $this->boolean('remember');
    }

    /**
     * Get the device name for token creation.
     */
    public function getDeviceName(): string
    {
        return $this->input('device_name', 'Unknown Device');
    }
}
