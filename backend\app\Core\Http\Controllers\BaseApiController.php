<?php

namespace App\Core\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

abstract class BaseApiController extends Controller
{
    /**
     * Return success response
     */
    protected function successResponse(
        $data = null, 
        string $message = 'Success', 
        int $status = 200
    ): JsonResponse {
        $response = [
            'success' => true,
            'message' => $message,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return response()->json($response, $status);
    }

    /**
     * Return error response
     */
    protected function errorResponse(
        string $message = 'Error occurred', 
        int $status = 400, 
        array $errors = []
    ): JsonResponse {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $status);
    }

    /**
     * Return resource response
     */
    protected function resourceResponse(
        JsonResource $resource, 
        string $message = 'Success', 
        int $status = 200
    ): JsonResponse {
        return $this->successResponse($resource, $message, $status);
    }

    /**
     * Return collection response
     */
    protected function collectionResponse(
        AnonymousResourceCollection $collection, 
        string $message = 'Success'
    ): JsonResponse {
        return $this->successResponse($collection, $message);
    }

    /**
     * Return paginated response
     */
    protected function paginatedResponse(
        LengthAwarePaginator $paginator, 
        string $resourceClass,
        string $message = 'Success'
    ): JsonResponse {
        return $this->successResponse([
            'data' => $resourceClass::collection($paginator->items()),
            'pagination' => [
                'current_page' => $paginator->currentPage(),
                'last_page' => $paginator->lastPage(),
                'per_page' => $paginator->perPage(),
                'total' => $paginator->total(),
                'from' => $paginator->firstItem(),
                'to' => $paginator->lastItem(),
            ],
            'links' => [
                'first' => $paginator->url(1),
                'last' => $paginator->url($paginator->lastPage()),
                'prev' => $paginator->previousPageUrl(),
                'next' => $paginator->nextPageUrl(),
            ]
        ], $message);
    }

    /**
     * Return created response
     */
    protected function createdResponse(
        $data = null, 
        string $message = 'Created successfully'
    ): JsonResponse {
        return $this->successResponse($data, $message, 201);
    }

    /**
     * Return updated response
     */
    protected function updatedResponse(
        $data = null, 
        string $message = 'Updated successfully'
    ): JsonResponse {
        return $this->successResponse($data, $message);
    }

    /**
     * Return deleted response
     */
    protected function deletedResponse(
        string $message = 'Deleted successfully'
    ): JsonResponse {
        return $this->successResponse(null, $message);
    }

    /**
     * Return not found response
     */
    protected function notFoundResponse(
        string $message = 'Resource not found'
    ): JsonResponse {
        return $this->errorResponse($message, 404);
    }

    /**
     * Return unauthorized response
     */
    protected function unauthorizedResponse(
        string $message = 'Unauthorized'
    ): JsonResponse {
        return $this->errorResponse($message, 401);
    }

    /**
     * Return forbidden response
     */
    protected function forbiddenResponse(
        string $message = 'Forbidden'
    ): JsonResponse {
        return $this->errorResponse($message, 403);
    }

    /**
     * Return validation error response
     */
    protected function validationErrorResponse(
        array $errors, 
        string $message = 'Validation failed'
    ): JsonResponse {
        return $this->errorResponse($message, 422, $errors);
    }

    /**
     * Return server error response
     */
    protected function serverErrorResponse(
        string $message = 'Internal server error'
    ): JsonResponse {
        return $this->errorResponse($message, 500);
    }

    /**
     * Handle exceptions and return appropriate response
     */
    protected function handleException(\Exception $e): JsonResponse
    {
        \Log::error('API Exception: ' . $e->getMessage(), [
            'trace' => $e->getTraceAsString()
        ]);

        if ($e instanceof \Illuminate\Database\Eloquent\ModelNotFoundException) {
            return $this->notFoundResponse('Resource not found');
        }

        if ($e instanceof \Illuminate\Validation\ValidationException) {
            return $this->validationErrorResponse(
                $e->errors(), 
                'Validation failed'
            );
        }

        if ($e instanceof \Illuminate\Auth\AuthenticationException) {
            return $this->unauthorizedResponse('Authentication required');
        }

        if ($e instanceof \Illuminate\Auth\Access\AuthorizationException) {
            return $this->forbiddenResponse('Access denied');
        }

        return $this->serverErrorResponse(
            app()->environment('production') 
                ? 'Something went wrong' 
                : $e->getMessage()
        );
    }
}
