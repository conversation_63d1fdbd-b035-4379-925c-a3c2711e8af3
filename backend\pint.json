{"preset": "laravel", "rules": {"simplified_null_return": true, "braces": {"position_after_control_structures": "same"}, "new_with_braces": true, "no_unused_imports": true, "ordered_imports": {"sort_algorithm": "alpha"}, "single_quote": true, "trailing_comma_in_multiline": true, "array_syntax": {"syntax": "short"}, "binary_operator_spaces": {"default": "single_space"}, "blank_line_after_namespace": true, "blank_line_after_opening_tag": true, "blank_line_before_statement": {"statements": ["return"]}, "cast_spaces": true, "class_attributes_separation": {"elements": {"method": "one"}}, "concat_space": {"spacing": "one"}, "declare_equal_normalize": true, "elseif": true, "encoding": true, "full_opening_tag": true, "function_declaration": {"closure_function_spacing": "one"}, "include": true, "increment_style": {"style": "post"}, "indentation_type": true, "line_ending": true, "lowercase_cast": true, "lowercase_keywords": true, "method_argument_space": {"on_multiline": "ensure_fully_multiline"}, "native_function_casing": true, "no_alias_functions": true, "no_closing_tag": true, "no_empty_phpdoc": true, "no_empty_statement": true, "no_extra_blank_lines": {"tokens": ["extra", "throw", "use"]}, "no_leading_import_slash": true, "no_leading_namespace_whitespace": true, "no_mixed_echo_print": {"use": "echo"}, "no_multiline_whitespace_around_double_arrow": true, "no_short_bool_cast": true, "no_singleline_whitespace_before_semicolons": true, "no_spaces_after_function_name": true, "no_spaces_around_offset": {"positions": ["inside", "outside"]}, "no_spaces_inside_parenthesis": true, "no_trailing_whitespace": true, "no_trailing_whitespace_in_comment": true, "no_unneeded_control_parentheses": true, "no_unreachable_default_argument_value": true, "no_useless_return": true, "no_whitespace_before_comma_in_array": true, "no_whitespace_in_blank_line": true, "normalize_index_brace": true, "object_operator_without_whitespace": true, "php_unit_fqcn_annotation": true, "phpdoc_align": {"align": "left"}, "phpdoc_annotation_without_dot": true, "phpdoc_indent": true, "phpdoc_inline_tag_normalizer": true, "phpdoc_no_access": true, "phpdoc_no_alias_tag": true, "phpdoc_no_empty_return": true, "phpdoc_no_package": true, "phpdoc_no_useless_inheritdoc": true, "phpdoc_return_self_reference": true, "phpdoc_scalar": true, "phpdoc_separation": true, "phpdoc_single_line_var_spacing": true, "phpdoc_summary": true, "phpdoc_to_comment": true, "phpdoc_trim": true, "phpdoc_types": true, "phpdoc_var_without_name": true, "return_type_declaration": true, "semicolon_after_instruction": true, "short_scalar_cast": true, "single_blank_line_at_eof": true, "single_blank_line_before_namespace": true, "single_class_element_per_statement": {"elements": ["property"]}, "single_import_per_statement": true, "single_line_after_imports": true, "single_line_comment_style": {"comment_types": ["hash"]}, "space_after_semicolon": {"remove_in_empty_for_expressions": true}, "standardize_not_equals": true, "switch_case_semicolon_to_colon": true, "switch_case_space": true, "ternary_operator_spaces": true, "trim_array_spaces": true, "unary_operator_spaces": true, "visibility_required": true, "whitespace_after_comma_in_array": true}, "exclude": ["vendor", "storage", "bootstrap/cache", "node_modules"]}