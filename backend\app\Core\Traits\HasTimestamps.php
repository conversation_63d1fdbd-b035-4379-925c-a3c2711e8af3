<?php

namespace App\Core\Traits;

trait HasTimestamps
{
    /**
     * Get formatted created at date
     */
    public function getFormattedCreatedAtAttribute(): string
    {
        return $this->created_at?->format('d M Y H:i') ?? '';
    }

    /**
     * Get formatted updated at date
     */
    public function getFormattedUpdatedAtAttribute(): string
    {
        return $this->updated_at?->format('d M Y H:i') ?? '';
    }

    /**
     * Get human readable created at date
     */
    public function getCreatedAtHumanAttribute(): string
    {
        return $this->created_at?->diffForHumans() ?? '';
    }

    /**
     * Get human readable updated at date
     */
    public function getUpdatedAtHumanAttribute(): string
    {
        return $this->updated_at?->diffForHumans() ?? '';
    }

    /**
     * Scope to filter by date range
     */
    public function scopeDateRange($query, $from = null, $to = null)
    {
        if ($from) {
            $query->where('created_at', '>=', $from);
        }

        if ($to) {
            $query->where('created_at', '<=', $to);
        }

        return $query;
    }

    /**
     * Scope to filter recent records
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Scope to order by latest
     */
    public function scopeLatest($query, string $column = 'created_at')
    {
        return $query->orderBy($column, 'desc');
    }

    /**
     * Scope to order by oldest
     */
    public function scopeOldest($query, string $column = 'created_at')
    {
        return $query->orderBy($column, 'asc');
    }
}
