<?php

namespace App\Core\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

abstract class BaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator): void
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422)
        );
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'required' => 'The :attribute field is required.',
            'string' => 'The :attribute must be a string.',
            'email' => 'The :attribute must be a valid email address.',
            'unique' => 'The :attribute has already been taken.',
            'min' => 'The :attribute must be at least :min characters.',
            'max' => 'The :attribute may not be greater than :max characters.',
            'confirmed' => 'The :attribute confirmation does not match.',
            'exists' => 'The selected :attribute is invalid.',
            'in' => 'The selected :attribute is invalid.',
            'numeric' => 'The :attribute must be a number.',
            'integer' => 'The :attribute must be an integer.',
            'boolean' => 'The :attribute field must be true or false.',
            'array' => 'The :attribute must be an array.',
            'file' => 'The :attribute must be a file.',
            'image' => 'The :attribute must be an image.',
            'mimes' => 'The :attribute must be a file of type: :values.',
            'size' => 'The :attribute must be :size kilobytes.',
            'between' => 'The :attribute must be between :min and :max.',
            'date' => 'The :attribute is not a valid date.',
            'after' => 'The :attribute must be a date after :date.',
            'before' => 'The :attribute must be a date before :date.',
            'regex' => 'The :attribute format is invalid.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'email' => 'email address',
            'first_name' => 'first name',
            'last_name' => 'last name',
            'phone_number' => 'phone number',
            'date_of_birth' => 'date of birth',
            'is_active' => 'status',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean and prepare data before validation
        $this->merge([
            'email' => strtolower($this->email ?? ''),
        ]);

        // Remove empty strings and convert to null
        $input = $this->all();
        array_walk_recursive($input, function (&$value) {
            if ($value === '') {
                $value = null;
            }
        });
        $this->replace($input);
    }

    /**
     * Get validated data with only fillable fields
     */
    public function getValidatedData(array $fillable = []): array
    {
        $validated = $this->validated();

        if (empty($fillable)) {
            return $validated;
        }

        return array_intersect_key($validated, array_flip($fillable));
    }

    /**
     * Get sanitized input
     */
    public function getSanitizedInput(string $key, $default = null)
    {
        $value = $this->input($key, $default);

        if (is_string($value)) {
            return trim(strip_tags($value));
        }

        return $value;
    }

    /**
     * Check if request is for update operation
     */
    public function isUpdate(): bool
    {
        return in_array($this->method(), ['PUT', 'PATCH']);
    }

    /**
     * Check if request is for create operation
     */
    public function isCreate(): bool
    {
        return $this->method() === 'POST';
    }

    /**
     * Get pagination parameters
     */
    public function getPaginationParams(): array
    {
        return [
            'page' => max(1, (int) $this->input('page', 1)),
            'per_page' => min(100, max(1, (int) $this->input('per_page', 15))),
            'search' => $this->input('search'),
            'sort_by' => $this->input('sort_by', 'created_at'),
            'sort_order' => in_array($this->input('sort_order'), ['asc', 'desc']) 
                ? $this->input('sort_order') 
                : 'desc',
        ];
    }

    /**
     * Get filter parameters
     */
    public function getFilterParams(): array
    {
        return array_filter([
            'status' => $this->input('status'),
            'category' => $this->input('category'),
            'date_from' => $this->input('date_from'),
            'date_to' => $this->input('date_to'),
        ]);
    }
}
