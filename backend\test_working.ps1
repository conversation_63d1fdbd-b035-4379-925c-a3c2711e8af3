# Working SantriMental Application Test
$baseUrl = "http://127.0.0.1:8000/api"
$headers = @{
    "Accept" = "application/json"
    "Content-Type" = "application/json"
}

Write-Host "=== SANTRIMENTAL APPLICATION - WORKING TEST ===" -ForegroundColor Green
Write-Host "Server: http://127.0.0.1:8000" -ForegroundColor Yellow

# Test 1: Application Health
Write-Host "`n1. Application Health Check" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:8000" -Method GET
    Write-Host "SUCCESS: Laravel application is running (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "FAILED: Application not accessible" -ForegroundColor Red
    exit 1
}

# Test 2: API Endpoint Check
Write-Host "`n2. API Endpoint Check" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/assessments/available" -Method GET -Headers $headers
    Write-Host "UNEXPECTED: Got $($response.StatusCode) instead of 401" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "SUCCESS: API endpoints are protected (401 Unauthorized)" -ForegroundColor Green
    } else {
        Write-Host "INFO: Got $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
    }
}

# Test 3: User Registration (with proper validation)
Write-Host "`n3. User Registration Test" -ForegroundColor Yellow

# Generate unique email to avoid conflicts
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$testEmail = "test$<EMAIL>"

try {
    $registerData = @{
        first_name = "Test"
        last_name = "User"
        email = $testEmail
        password = "Password123!"
        password_confirmation = "Password123!"
        phone = "081234567890"
        student_id = "TST$timestamp"
        class = "12A"
        grade = 12
        role = "student"
        terms_accepted = $true
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "$baseUrl/auth/register" -Method POST -Headers $headers -Body $registerData
    Write-Host "SUCCESS: User registration (Status: $($response.StatusCode))" -ForegroundColor Green
    
    $data = $response.Content | ConvertFrom-Json
    Write-Host "User: $($data.data.user.full_name)" -ForegroundColor Cyan
    Write-Host "Email: $($data.data.user.email)" -ForegroundColor Cyan
    Write-Host "Role: $($data.data.user.role.display_name)" -ForegroundColor Cyan
    $userToken = $data.data.token
    Write-Host "Token generated successfully" -ForegroundColor Green
} catch {
    Write-Host "FAILED: Registration error - $($_.Exception.Message)" -ForegroundColor Red
    
    # Try to get more details about the error
    try {
        $errorResponse = $_.Exception.Response
        $reader = New-Object System.IO.StreamReader($errorResponse.GetResponseStream())
        $errorContent = $reader.ReadToEnd()
        $errorData = $errorContent | ConvertFrom-Json
        Write-Host "Error details: $($errorData.message)" -ForegroundColor Yellow
        if ($errorData.errors) {
            foreach ($field in $errorData.errors.PSObject.Properties) {
                Write-Host "  $($field.Name): $($field.Value -join ', ')" -ForegroundColor Yellow
            }
        }
    } catch {
        Write-Host "Could not parse error details" -ForegroundColor Yellow
    }
    $userToken = $null
}

# Test 4: User Login
if ($userToken) {
    Write-Host "`n4. User Login Test" -ForegroundColor Yellow
    try {
        $loginData = @{
            login = $testEmail
            password = "Password123!"
            device_name = "Test Device"
        } | ConvertTo-Json
        
        $response = Invoke-WebRequest -Uri "$baseUrl/auth/login" -Method POST -Headers $headers -Body $loginData
        Write-Host "SUCCESS: User login (Status: $($response.StatusCode))" -ForegroundColor Green
        
        $data = $response.Content | ConvertFrom-Json
        $loginToken = $data.data.token
        Write-Host "Login successful for: $($data.data.user.full_name)" -ForegroundColor Cyan
    } catch {
        Write-Host "FAILED: Login error - $($_.Exception.Message)" -ForegroundColor Red
        $loginToken = $userToken
    }
} else {
    Write-Host "`n4. Skipping login test (registration failed)" -ForegroundColor Yellow
    $loginToken = $null
}

# Test 5: Get User Profile
if ($loginToken) {
    Write-Host "`n5. User Profile Test" -ForegroundColor Yellow
    try {
        $authHeaders = $headers.Clone()
        $authHeaders["Authorization"] = "Bearer $loginToken"
        
        $response = Invoke-WebRequest -Uri "$baseUrl/auth/user" -Method GET -Headers $authHeaders
        Write-Host "SUCCESS: Profile retrieval (Status: $($response.StatusCode))" -ForegroundColor Green
        
        $data = $response.Content | ConvertFrom-Json
        Write-Host "Profile: $($data.data.full_name)" -ForegroundColor Cyan
        Write-Host "Student ID: $($data.data.student_id)" -ForegroundColor Cyan
        Write-Host "Class: $($data.data.class)" -ForegroundColor Cyan
    } catch {
        Write-Host "FAILED: Profile error - $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "`n5. Skipping profile test (no valid token)" -ForegroundColor Yellow
}

# Test 6: Assessment Forms
if ($loginToken) {
    Write-Host "`n6. Assessment Forms Test" -ForegroundColor Yellow
    try {
        $authHeaders = $headers.Clone()
        $authHeaders["Authorization"] = "Bearer $loginToken"
        
        $response = Invoke-WebRequest -Uri "$baseUrl/assessments/available" -Method GET -Headers $authHeaders
        Write-Host "SUCCESS: Assessment forms (Status: $($response.StatusCode))" -ForegroundColor Green
        
        $data = $response.Content | ConvertFrom-Json
        Write-Host "Available forms: $($data.data.Count)" -ForegroundColor Cyan
        
        $count = 0
        foreach ($form in $data.data) {
            $count++
            Write-Host "  $count. $($form.code): $($form.name)" -ForegroundColor White
        }
    } catch {
        Write-Host "FAILED: Assessment forms error - $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "`n6. Skipping assessment test (no valid token)" -ForegroundColor Yellow
}

# Summary
Write-Host "`n=== TEST SUMMARY ===" -ForegroundColor Green
if ($userToken) {
    Write-Host "SUCCESS: SantriMental application is fully functional!" -ForegroundColor Green
    Write-Host "- Laravel server: Running" -ForegroundColor Green
    Write-Host "- Database: Connected" -ForegroundColor Green
    Write-Host "- Authentication: Working" -ForegroundColor Green
    Write-Host "- Assessment system: Available" -ForegroundColor Green
    Write-Host "`nAPI Base URL: http://127.0.0.1:8000/api" -ForegroundColor Cyan
    Write-Host "Application URL: http://127.0.0.1:8000" -ForegroundColor Cyan
} else {
    Write-Host "PARTIAL: Application is running but registration needs debugging" -ForegroundColor Yellow
    Write-Host "- Laravel server: Running" -ForegroundColor Green
    Write-Host "- API endpoints: Protected" -ForegroundColor Green
    Write-Host "- Registration: Needs attention" -ForegroundColor Yellow
}

Write-Host "`nApplication is ready for development and testing!" -ForegroundColor Green
