@echo off
echo ========================================
echo SantriMental - Backup Before Fresh Start
echo ========================================

set BACKUP_DIR=C:\laragon\www\santrimental-backup-%date:~-4,4%%date:~-10,2%%date:~-7,2%
set PROJECT_DIR=C:\laragon\www\santrimental

echo Creating backup directory: %BACKUP_DIR%
mkdir "%BACKUP_DIR%" 2>nul

echo.
echo Backing up important files...

REM Backup database (if exists)
if exist "%PROJECT_DIR%\backend\.env" (
    echo - Backing up .env file
    copy "%PROJECT_DIR%\backend\.env" "%BACKUP_DIR%\.env.backup"
)

REM Backup custom configurations
if exist "%PROJECT_DIR%\backend\config" (
    echo - Backing up config directory
    xcopy "%PROJECT_DIR%\backend\config" "%BACKUP_DIR%\config\" /E /I /Q
)

REM Backup storage (uploads, logs)
if exist "%PROJECT_DIR%\backend\storage" (
    echo - Backing up storage directory
    xcopy "%PROJECT_DIR%\backend\storage" "%BACKUP_DIR%\storage\" /E /I /Q
)

REM Backup database file (if SQLite)
if exist "%PROJECT_DIR%\backend\database\database.sqlite" (
    echo - Backing up SQLite database
    copy "%PROJECT_DIR%\backend\database\database.sqlite" "%BACKUP_DIR%\database.sqlite.backup"
)

REM Backup custom public assets
if exist "%PROJECT_DIR%\backend\public\uploads" (
    echo - Backing up uploads
    xcopy "%PROJECT_DIR%\backend\public\uploads" "%BACKUP_DIR%\uploads\" /E /I /Q
)

echo.
echo Backup completed: %BACKUP_DIR%
echo.
echo You can now safely proceed with fresh start!
echo.
pause
