# Apache Virtual Host Configuration for SantriMental
# Copy this file to: C:\laragon\etc\apache2\sites-enabled\

<VirtualHost *:80>
    ServerName santrimental.me
    ServerAlias www.santrimental.me
    DocumentRoot "C:/laragon/www/santrimental/backend/public"
    
    <Directory "C:/laragon/www/santrimental/backend/public">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        DirectoryIndex index.php
        
        # Enable URL rewriting
        RewriteEngine On
        
        # Handle Authorization Header
        RewriteCond %{HTTP:Authorization} .
        RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
        
        # Redirect Trailing Slashes If Not A Folder
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteCond %{REQUEST_URI} (.+)/$
        RewriteRule ^ %1 [L,R=301]
        
        # Send Requests To Front Controller
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteRule ^ index.php [L]
    </Directory>
    
    # Logging
    ErrorLog "C:/laragon/logs/santrimental_error.log"
    CustomLog "C:/laragon/logs/santrimental_access.log" combined
    
    # Security Headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</VirtualHost>

# Alternative configuration for localhost/santrimental
<VirtualHost *:80>
    ServerName localhost
    DocumentRoot "C:/laragon/www"
    
    # Alias for santrimental
    Alias /santrimental "C:/laragon/www/santrimental/backend/public"
    
    <Directory "C:/laragon/www/santrimental/backend/public">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        DirectoryIndex index.php
    </Directory>
</VirtualHost>
