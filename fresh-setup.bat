@echo off
echo ========================================
echo SantriMental Fresh Setup Script
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo [1/6] Backing up current configuration...
if exist "backup" rmdir /s /q "backup"
mkdir "backup"
if exist ".htaccess" copy ".htaccess" "backup\.htaccess.old"
if exist "santrimental.me.conf" copy "santrimental.me.conf" "backup\santrimental.me.conf.old"

echo [2/6] Cleaning old configurations...
REM Remove old .htaccess if exists
if exist ".htaccess" del ".htaccess"

echo [3/6] Creating optimized .htaccess...
(
echo ^<IfModule mod_rewrite.c^>
echo     RewriteEngine On
echo.
echo     # Security: Block access to sensitive files
echo     RewriteRule ^backend/\.env$ - [F,L]
echo     RewriteRule ^backend/storage/ - [F,L]
echo     RewriteRule ^backend/bootstrap/cache/ - [F,L]
echo.
echo     # Handle static assets in root
echo     RewriteCond %%{REQUEST_FILENAME} -f
echo     RewriteRule ^.*$ - [L]
echo.
echo     # Redirect everything else to Laravel backend/public
echo     RewriteCond %%{REQUEST_URI} !^/backend/public/
echo     RewriteRule ^(.*)$ /backend/public/$1 [L,QSA]
echo ^</IfModule^>
) > .htaccess

echo [4/6] Creating Apache Virtual Host configuration...
(
echo # SantriMental Virtual Host Configuration
echo # Copy this file to: C:\laragon\etc\apache2\sites-enabled\santrimental.me.conf
echo.
echo ^<VirtualHost *:80^>
echo     ServerName santrimental.me
echo     ServerAlias www.santrimental.me
echo     DocumentRoot "C:/laragon/www/santrimental/backend/public"
echo.
echo     ^<Directory "C:/laragon/www/santrimental/backend/public"^>
echo         AllowOverride All
echo         Require all granted
echo         DirectoryIndex index.php
echo         Options -Indexes +FollowSymLinks
echo.
echo         # Laravel URL Rewriting
echo         RewriteEngine On
echo         RewriteCond %%{REQUEST_FILENAME} !-f
echo         RewriteCond %%{REQUEST_FILENAME} !-d
echo         RewriteRule ^(.*)$ index.php [L,QSA]
echo     ^</Directory^>
echo.
echo     # Security headers
echo     Header always set X-Content-Type-Options nosniff
echo     Header always set X-Frame-Options DENY
echo     Header always set X-XSS-Protection "1; mode=block"
echo.
echo     # Logging
echo     ErrorLog "C:/laragon/logs/santrimental_error.log"
echo     CustomLog "C:/laragon/logs/santrimental_access.log" combined
echo ^</VirtualHost^>
echo.
echo # Additional configuration for localhost access
echo ^<VirtualHost *:80^>
echo     ServerName localhost
echo     DocumentRoot "C:/laragon/www"
echo.
echo     # Alias for santrimental project
echo     Alias /santrimental "C:/laragon/www/santrimental/backend/public"
echo.
echo     ^<Directory "C:/laragon/www/santrimental/backend/public"^>
echo         AllowOverride All
echo         Require all granted
echo         DirectoryIndex index.php
echo         Options -Indexes +FollowSymLinks
echo.
echo         RewriteEngine On
echo         RewriteCond %%{REQUEST_FILENAME} !-f
echo         RewriteCond %%{REQUEST_FILENAME} !-d
echo         RewriteRule ^(.*)$ index.php [L,QSA]
echo     ^</Directory^>
echo ^</VirtualHost^>
) > santrimental.me.conf

echo [5/6] Updating hosts file...
REM Check if entry already exists
findstr /C:"santrimental.me" "C:\Windows\System32\drivers\etc\hosts" >nul 2>&1
if %errorLevel% neq 0 (
    echo Adding santrimental.me to hosts file...
    echo. >> "C:\Windows\System32\drivers\etc\hosts"
    echo # SantriMental Local Development >> "C:\Windows\System32\drivers\etc\hosts"
    echo 127.0.0.1    santrimental.me >> "C:\Windows\System32\drivers\etc\hosts"
    echo 127.0.0.1    www.santrimental.me >> "C:\Windows\System32\drivers\etc\hosts"
) else (
    echo santrimental.me already exists in hosts file
)

echo [6/6] Setting up Laravel environment...
cd backend
if exist ".env" (
    echo Laravel .env file exists
) else (
    if exist ".env.example" (
        copy ".env.example" ".env"
        echo Created .env from .env.example
    )
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Copy santrimental.me.conf to C:\laragon\etc\apache2\sites-enabled\
echo 2. Restart Laragon Apache
echo 3. Access your application:
echo    - http://localhost/santrimental
echo    - http://santrimental.me
echo.
echo Project structure:
echo ├── backend/public/     ← Laravel application home
echo ├── frontend/           ← Frontend assets (if any)
echo ├── DOCS/              ← Documentation
echo └── .htaccess          ← Root redirect configuration
echo.
pause
