<?php

namespace App\Modules;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\File;

class ModuleServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->registerModules();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->bootModules();
    }

    /**
     * Register all modules
     */
    private function registerModules(): void
    {
        $modules = $this->getModules();
        
        foreach ($modules as $module) {
            $this->registerModule($module);
        }
    }

    /**
     * Boot all modules
     */
    private function bootModules(): void
    {
        $modules = $this->getModules();
        
        foreach ($modules as $module) {
            $this->bootModule($module);
        }
    }

    /**
     * Get all available modules
     */
    private function getModules(): array
    {
        $modulesPath = app_path('Modules');
        
        if (!File::exists($modulesPath)) {
            return [];
        }

        $modules = [];
        $directories = File::directories($modulesPath);

        foreach ($directories as $directory) {
            $moduleName = basename($directory);
            
            // Skip if not a valid module (no Providers directory)
            if (!File::exists($directory . '/Providers')) {
                continue;
            }

            $modules[] = $moduleName;
        }

        return $modules;
    }

    /**
     * Register a specific module
     */
    private function registerModule(string $module): void
    {
        $providerClass = "App\\Modules\\{$module}\\Providers\\{$module}ServiceProvider";
        
        if (class_exists($providerClass)) {
            $this->app->register($providerClass);
        }
    }

    /**
     * Boot a specific module
     */
    private function bootModule(string $module): void
    {
        // Load module routes
        $this->loadModuleRoutes($module);
        
        // Load module views
        $this->loadModuleViews($module);
        
        // Load module migrations
        $this->loadModuleMigrations($module);
        
        // Load module translations
        $this->loadModuleTranslations($module);
    }

    /**
     * Load module routes
     */
    private function loadModuleRoutes(string $module): void
    {
        $routesPath = app_path("Modules/{$module}/routes");
        
        if (File::exists($routesPath)) {
            // Load web routes
            $webRoutes = $routesPath . '/web.php';
            if (File::exists($webRoutes)) {
                $this->loadRoutesFrom($webRoutes);
            }
            
            // Load API routes
            $apiRoutes = $routesPath . '/api.php';
            if (File::exists($apiRoutes)) {
                $this->loadRoutesFrom($apiRoutes);
            }
        }
    }

    /**
     * Load module views
     */
    private function loadModuleViews(string $module): void
    {
        $viewsPath = app_path("Modules/{$module}/resources/views");
        
        if (File::exists($viewsPath)) {
            $this->loadViewsFrom($viewsPath, strtolower($module));
        }
    }

    /**
     * Load module migrations
     */
    private function loadModuleMigrations(string $module): void
    {
        $migrationsPath = app_path("Modules/{$module}/database/migrations");
        
        if (File::exists($migrationsPath)) {
            $this->loadMigrationsFrom($migrationsPath);
        }
    }

    /**
     * Load module translations
     */
    private function loadModuleTranslations(string $module): void
    {
        $translationsPath = app_path("Modules/{$module}/resources/lang");
        
        if (File::exists($translationsPath)) {
            $this->loadTranslationsFrom($translationsPath, strtolower($module));
        }
    }
}
