@echo off
echo ========================================
echo SantriMental Composer Package Creator
echo ========================================
echo.

set "MODULES_DIR=%cd%\santrimental-modules"
set "PACKAGE_DIR=%cd%\santrimental-package"

if not exist "%MODULES_DIR%" (
    echo ERROR: Modules not extracted yet!
    echo Please run extract-modules.bat first
    pause
    exit /b 1
)

echo Creating Composer package...
if exist "%PACKAGE_DIR%" rmdir /s /q "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%"

echo.
echo [1/4] Creating package structure...
mkdir "%PACKAGE_DIR%\src"
mkdir "%PACKAGE_DIR%\src\Console"
mkdir "%PACKAGE_DIR%\src\Providers"
mkdir "%PACKAGE_DIR%\resources"
mkdir "%PACKAGE_DIR%\database"
mkdir "%PACKAGE_DIR%\public"

echo [2/4] Copying modules...
xcopy "%MODULES_DIR%\resources" "%PACKAGE_DIR%\resources" /E /I /H /Y
xcopy "%MODULES_DIR%\database" "%PACKAGE_DIR%\database" /E /I /H /Y
xcopy "%MODULES_DIR%\public" "%PACKAGE_DIR%\public" /E /I /H /Y
xcopy "%MODULES_DIR%\app" "%PACKAGE_DIR%\src" /E /I /H /Y

echo [3/4] Creating composer.json...
(
echo {
echo     "name": "santrimental/core",
echo     "description": "SantriMental Mental Health Assessment Platform - Core Modules",
echo     "type": "library",
echo     "license": "MIT",
echo     "authors": [
echo         {
echo             "name": "SantriMental Team",
echo             "email": "<EMAIL>"
echo         }
echo     ],
echo     "require": {
echo         "php": "^8.1",
echo         "laravel/framework": "^10.0",
echo         "laravel/sanctum": "^3.0"
echo     },
echo     "autoload": {
echo         "psr-4": {
echo             "SantriMental\\Core\\": "src/"
echo         }
echo     },
echo     "extra": {
echo         "laravel": {
echo             "providers": [
echo                 "SantriMental\\Core\\Providers\\SantriMentalServiceProvider"
echo             ]
echo         }
echo     },
echo     "minimum-stability": "stable",
echo     "prefer-stable": true
echo }
) > "%PACKAGE_DIR%\composer.json"

echo [4/4] Creating Service Provider...
(
echo ^<?php
echo.
echo namespace SantriMental\Core\Providers;
echo.
echo use Illuminate\Support\ServiceProvider;
echo.
echo class SantriMentalServiceProvider extends ServiceProvider
echo {
echo     public function register^(^)
echo     {
echo         // Register services
echo     }
echo.
echo     public function boot^(^)
echo     {
echo         // Publish views
echo         $this-^>publishes^([
echo             __DIR__.'/../../resources/views' =^> resource_path^('views'^),
echo         ], 'santrimental-views'^);
echo.
echo         // Publish assets
echo         $this-^>publishes^([
echo             __DIR__.'/../../public' =^> public_path^(^),
echo         ], 'santrimental-assets'^);
echo.
echo         // Publish migrations
echo         $this-^>publishes^([
echo             __DIR__.'/../../database/migrations' =^> database_path^('migrations'^),
echo         ], 'santrimental-migrations'^);
echo.
echo         // Publish seeders
echo         $this-^>publishes^([
echo             __DIR__.'/../../database/seeders' =^> database_path^('seeders'^),
echo         ], 'santrimental-seeders'^);
echo.
echo         // Load routes
echo         $this-^>loadRoutesFrom^(__DIR__.'/../../routes/web.php'^);
echo         $this-^>loadRoutesFrom^(__DIR__.'/../../routes/api.php'^);
echo     }
echo }
) > "%PACKAGE_DIR%\src\Providers\SantriMentalServiceProvider.php"

echo.
echo Creating installation command...
(
echo ^<?php
echo.
echo namespace SantriMental\Core\Console;
echo.
echo use Illuminate\Console\Command;
echo.
echo class InstallCommand extends Command
echo {
echo     protected $signature = 'santrimental:install';
echo     protected $description = 'Install SantriMental modules';
echo.
echo     public function handle^(^)
echo     {
echo         $this-^>info^('Installing SantriMental modules...'^);
echo.
echo         // Publish all assets
echo         $this-^>call^('vendor:publish', [
echo             '--provider' =^> 'SantriMental\Core\Providers\SantriMentalServiceProvider',
echo             '--force' =^> true
echo         ]^);
echo.
echo         $this-^>info^('SantriMental installed successfully!'^);
echo         $this-^>info^('Run: php artisan migrate'^);
echo         $this-^>info^('Run: php artisan db:seed'^);
echo     }
echo }
) > "%PACKAGE_DIR%\src\Console\InstallCommand.php"

echo.
echo Creating README for package...
(
echo # SantriMental Core Package
echo.
echo Mental Health Assessment Platform for Laravel
echo.
echo ## Installation
echo.
echo ```bash
echo composer require santrimental/core
echo php artisan santrimental:install
echo php artisan migrate
echo php artisan db:seed
echo ```
echo.
echo ## Features
echo.
echo - Multi-role dashboards
echo - Dynamic form system
echo - Mental health assessments
echo - Modern responsive UI
echo - Role-based permissions
echo.
echo ## Usage
echo.
echo After installation, visit:
echo - `/` - Home page
echo - `/dashboard` - Student dashboard
echo - `/admin/dashboard` - Admin dashboard
echo - `/guru/dashboard` - Teacher dashboard
echo - `/orangtua/dashboard` - Parent dashboard
echo.
echo ## Requirements
echo.
echo - PHP ^>= 8.1
echo - Laravel ^>= 10.0
echo - MySQL/PostgreSQL
) > "%PACKAGE_DIR%\README.md"

echo.
echo ========================================
echo Composer package created!
echo ========================================
echo.
echo Package location: %PACKAGE_DIR%
echo.
echo To use this package:
echo 1. Upload to Packagist or private repository
echo 2. Or install locally: composer config repositories.local path ../santrimental-package
echo 3. Then: composer require santrimental/core
echo.
pause
