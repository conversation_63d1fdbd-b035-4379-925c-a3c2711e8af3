<?php

namespace App\Modules\Assessment\Repositories;

use App\Core\Repositories\BaseRepository;
use App\Modules\Assessment\Models\AssessmentForm;
use App\Modules\Assessment\Models\AssessmentResponse;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class AssessmentRepository extends BaseRepository
{
    public function __construct(AssessmentForm $model)
    {
        parent::__construct($model);
    }

    /**
     * Find form by code
     */
    public function findByCode(string $code): ?AssessmentForm
    {
        return $this->model->where('code', $code)->first();
    }

    /**
     * Get forms by category
     */
    public function getByCategory(string $category): Collection
    {
        return $this->model->where('category', $category)
                          ->active()
                          ->orderBy('name')
                          ->get();
    }

    /**
     * Get available forms for user
     */
    public function getAvailableForUser(int $userId): Collection
    {
        return $this->model->active()
                          ->whereDoesntHave('responses', function ($query) use ($userId) {
                              $query->where('user_id', $userId)
                                   ->where('status', AssessmentResponse::STATUS_COMPLETED);
                          })
                          ->orderBy('name')
                          ->get();
    }

    /**
     * Get user's completed assessments
     */
    public function getUserCompletedAssessments(int $userId): Collection
    {
        return $this->model->whereHas('responses', function ($query) use ($userId) {
                              $query->where('user_id', $userId)
                                   ->where('status', AssessmentResponse::STATUS_COMPLETED);
                          })
                          ->with(['responses' => function ($query) use ($userId) {
                              $query->where('user_id', $userId)
                                   ->where('status', AssessmentResponse::STATUS_COMPLETED)
                                   ->latest();
                          }])
                          ->get();
    }

    /**
     * Get assessment statistics
     */
    public function getStatistics(): array
    {
        $totalForms = $this->model->count();
        $activeForms = $this->model->active()->count();
        $totalResponses = AssessmentResponse::count();
        $completedResponses = AssessmentResponse::completed()->count();

        return [
            'total_forms' => $totalForms,
            'active_forms' => $activeForms,
            'total_responses' => $totalResponses,
            'completed_responses' => $completedResponses,
            'completion_rate' => $totalResponses > 0 
                ? round(($completedResponses / $totalResponses) * 100, 2) 
                : 0,
        ];
    }

    /**
     * Apply search filters to query
     */
    protected function applySearch(Builder $query, string $search): Builder
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('code', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('category', 'like', "%{$search}%");
        });
    }

    /**
     * Get forms with response counts
     */
    public function getWithResponseCounts(): Collection
    {
        return $this->model->withCount([
                              'responses',
                              'responses as completed_responses_count' => function ($query) {
                                  $query->where('status', AssessmentResponse::STATUS_COMPLETED);
                              }
                          ])
                          ->get();
    }

    /**
     * Get popular forms (most completed)
     */
    public function getPopularForms(int $limit = 10): Collection
    {
        return $this->model->withCount([
                              'responses as completed_count' => function ($query) {
                                  $query->where('status', AssessmentResponse::STATUS_COMPLETED);
                              }
                          ])
                          ->active()
                          ->orderBy('completed_count', 'desc')
                          ->limit($limit)
                          ->get();
    }

    /**
     * Get recent forms
     */
    public function getRecentForms(int $limit = 10): Collection
    {
        return $this->model->active()
                          ->latest()
                          ->limit($limit)
                          ->get();
    }

    /**
     * Search forms with filters
     */
    public function searchWithFilters(array $filters): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        // Apply search
        if (!empty($filters['search'])) {
            $query = $this->applySearch($query, $filters['search']);
        }

        // Apply category filter
        if (!empty($filters['category'])) {
            $query->where('category', $filters['category']);
        }

        // Apply status filter
        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        // Apply date range filter
        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortOrder = $filters['sort_order'] ?? 'desc';
        $query->orderBy($sortBy, $sortOrder);

        // Apply pagination
        $perPage = min($filters['per_page'] ?? 15, 100);
        
        return $query->paginate($perPage);
    }
}
