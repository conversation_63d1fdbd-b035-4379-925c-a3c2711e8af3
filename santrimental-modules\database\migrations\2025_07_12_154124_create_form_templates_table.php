<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('form_templates', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique(); // SRQ20, GSE, MSCS, MHKQ, DASS42
            $table->string('name'); // Self Report Questionnaire 20
            $table->text('description')->nullable();
            $table->string('category')->default('mental_health'); // mental_health, personality, etc
            $table->json('questions'); // Array of questions with options
            $table->json('scoring_rules'); // Scoring configuration
            $table->json('interpretation_rules'); // Interpretation based on scores
            $table->integer('time_limit')->nullable(); // Time limit in minutes
            $table->boolean('is_active')->default(true);
            $table->integer('version')->default(1);
            $table->timestamps();
        });

        Schema::create('form_questions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('form_template_id')->constrained()->onDelete('cascade');
            $table->integer('question_number');
            $table->text('question_text');
            $table->string('question_type')->default('radio'); // radio, checkbox, text, scale
            $table->json('options')->nullable(); // For radio/checkbox options
            $table->boolean('is_required')->default(true);
            $table->integer('score_weight')->default(1);
            $table->timestamps();
        });

        Schema::create('form_responses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('form_template_id')->constrained()->onDelete('cascade');
            $table->json('answers'); // User responses
            $table->integer('total_score');
            $table->string('status'); // normal, concern, high_risk
            $table->text('interpretation')->nullable();
            $table->text('recommendations')->nullable();
            $table->integer('completion_time')->nullable(); // Time taken in seconds
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('form_responses');
        Schema::dropIfExists('form_questions');
        Schema::dropIfExists('form_templates');
    }
};
