/**
 * SRQ-20 Form Logic
 */
document.addEventListener('DOMContentLoaded', () => {
    const auth = window.auth;
    const Utils = window.Utils;

    // Check authentication
    if (!auth.isAuthenticated()) {
        window.location.href = '/';
        return;
    }

    // Elements
    const srqForm = document.getElementById('srqForm');
    const questionsContainer = document.getElementById('questions-container');
    const formSection = document.getElementById('form-section');
    const resultSection = document.getElementById('result-section');
    const scoreEl = document.getElementById('score');
    const interpretationEl = document.getElementById('interpretation');
    const recommendationEl = document.getElementById('recommendation');
    const resultContentEl = document.getElementById('result-content');
    const resultIconEl = document.getElementById('result-icon');
    const stepsListEl = document.getElementById('steps-list');
    const resetButton = document.getElementById('reset-button');
    const saveResultBtn = document.getElementById('save-result-btn');
    const alertMessage = document.getElementById('alert-message');
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const submitBtn = document.getElementById('submit-btn');
    const submitText = document.getElementById('submit-text');
    const submitLoading = document.getElementById('submit-loading');

    // SRQ-20 Questions
    const questions = [
        "Apakah Anda sering merasa sakit kepala?",
        "Apakah nafsu makan Anda buruk?",
        "Apakah Anda tidur tidak nyenyak?",
        "Apakah Anda mudah merasa takut?",
        "Apakah Anda merasa cemas, tegang, atau khawatir?",
        "Apakah tangan Anda gemetar?",
        "Apakah pencernaan Anda terganggu atau buruk?",
        "Apakah Anda sulit berpikir jernih?",
        "Apakah Anda merasa tidak bahagia?",
        "Apakah Anda lebih sering menangis?",
        "Apakah Anda merasa sulit untuk menikmati kegiatan sehari-hari?",
        "Apakah Anda mengalami kesulitan dalam mengambil keputusan?",
        "Apakah pekerjaan sehari-hari Anda terganggu?",
        "Apakah Anda tidak mampu melakukan hal-hal yang bermanfaat dalam hidup?",
        "Apakah Anda kehilangan minat pada berbagai hal?",
        "Apakah Anda merasa tidak berharga?",
        "Apakah Anda mempunyai pikiran untuk mengakhiri hidup Anda?",
        "Apakah Anda merasa lelah sepanjang waktu?",
        "Apakah Anda merasa tidak enak di perut?",
        "Apakah Anda mudah lelah?"
    ];

    let currentResult = null;

    // Function to generate question elements
    function renderQuestions() {
        let questionsHTML = '';
        questions.forEach((q, index) => {
            const questionNumber = index + 1;
            questionsHTML += `
                <div class="question-card glass-card p-6 rounded-xl">
                    <div class="flex items-start justify-between">
                        <div class="flex-1 mr-6">
                            <div class="flex items-center mb-3">
                                <span class="bg-purple-500 text-white text-sm font-bold rounded-full w-8 h-8 flex items-center justify-center mr-3">${questionNumber}</span>
                                <span class="text-purple-200 text-sm">Pertanyaan ${questionNumber} dari ${questions.length}</span>
                            </div>
                            <p class="text-white font-medium text-lg leading-relaxed">${q}</p>
                        </div>
                        <div class="flex flex-col space-y-3">
                            <div class="flex items-center">
                                <input type="radio" id="q${questionNumber}-yes" name="q${questionNumber}" value="1" class="sr-only" required>
                                <label for="q${questionNumber}-yes" class="custom-radio-label cursor-pointer w-20 text-center py-3 px-4 border-2 border-white/30 rounded-lg text-white hover:border-purple-400 transition-all duration-200">Ya</label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" id="q${questionNumber}-no" name="q${questionNumber}" value="0" class="sr-only" required>
                                <label for="q${questionNumber}-no" class="custom-radio-label cursor-pointer w-20 text-center py-3 px-4 border-2 border-white/30 rounded-lg text-white hover:border-purple-400 transition-all duration-200">Tidak</label>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        questionsContainer.innerHTML = questionsHTML;

        // Add event listeners for progress tracking
        const radioInputs = questionsContainer.querySelectorAll('input[type="radio"]');
        radioInputs.forEach(input => {
            input.addEventListener('change', updateProgress);
        });
    }

    // Update progress bar
    function updateProgress() {
        const answeredQuestions = new Set();
        const formData = new FormData(srqForm);
        
        for (let key of formData.keys()) {
            answeredQuestions.add(key);
        }

        const progress = (answeredQuestions.size / questions.length) * 100;
        progressBar.style.width = `${progress}%`;
        progressText.textContent = `${answeredQuestions.size}/${questions.length}`;
    }

    // Show loading state
    function showLoading() {
        submitBtn.disabled = true;
        submitText.textContent = 'Memproses...';
        submitLoading.classList.remove('hidden');
    }

    // Hide loading state
    function hideLoading() {
        submitBtn.disabled = false;
        submitText.textContent = 'Lihat Hasil';
        submitLoading.classList.add('hidden');
    }

    // Handle form submission
    srqForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const formData = new FormData(srqForm);
        const answeredQuestions = Array.from(formData.keys()).length;

        // Check if all questions are answered
        if (answeredQuestions < questions.length) {
            alertMessage.classList.remove('hidden');
            window.scrollTo({ top: 0, behavior: 'smooth' });
            return;
        }
        
        alertMessage.classList.add('hidden');
        showLoading();

        try {
            // Calculate score
            let score = 0;
            const answers = {};
            
            for (let [key, value] of formData.entries()) {
                if (key !== '_token') {
                    answers[key] = parseInt(value);
                    score += parseInt(value);
                }
            }

            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            currentResult = {
                score: score,
                answers: answers,
                total_questions: questions.length,
                timestamp: new Date().toISOString()
            };

            displayResult(score);
            
        } catch (error) {
            console.error('Error processing form:', error);
            Utils.showNotification('Terjadi kesalahan saat memproses form', 'error');
        } finally {
            hideLoading();
        }
    });

    // Function to display the result
    function displayResult(score) {
        scoreEl.textContent = score;

        let status, interpretation, recommendation, steps, iconHTML, bgClass;

        if (score >= 8) {
            status = 'high_risk';
            interpretation = "Terindikasi Mengalami Gangguan Mental yang Signifikan";
            recommendation = "Skor Anda menunjukkan adanya kemungkinan besar masalah kesehatan mental yang memerlukan perhatian serius. Sangat disarankan untuk segera berkonsultasi dengan profesional kesehatan mental.";
            steps = [
                "Segera konsultasi dengan psikolog atau psikiater",
                "Pertimbangkan untuk mendapatkan dukungan dari keluarga dan teman",
                "Hindari penggunaan alkohol atau zat terlarang",
                "Jaga pola tidur dan makan yang teratur",
                "Lakukan aktivitas fisik ringan secara rutin"
            ];
            iconHTML = `<svg class="w-12 h-12 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>`;
            bgClass = 'bg-red-500/20 border border-red-400';
        } else if (score >= 6) {
            status = 'concern';
            interpretation = "Terindikasi Mengalami Gangguan Emosional Ringan";
            recommendation = "Skor Anda menunjukkan adanya beberapa gejala yang perlu diperhatikan. Disarankan untuk melakukan evaluasi lebih lanjut dan mempertimbangkan konsultasi dengan profesional kesehatan mental.";
            steps = [
                "Pertimbangkan konsultasi dengan psikolog",
                "Praktikkan teknik relaksasi dan manajemen stres",
                "Jaga pola hidup sehat dan olahraga teratur",
                "Cari dukungan dari keluarga dan teman terdekat",
                "Monitor kondisi Anda dalam beberapa minggu ke depan"
            ];
            iconHTML = `<svg class="w-12 h-12 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>`;
            bgClass = 'bg-yellow-500/20 border border-yellow-400';
        } else {
            status = 'normal';
            interpretation = "Kondisi Kesehatan Mental Anda Baik";
            recommendation = "Skor Anda berada dalam rentang normal. Terus pertahankan gaya hidup sehat dan kelola stres dengan baik untuk menjaga kesehatan mental Anda.";
            steps = [
                "Pertahankan pola hidup sehat yang sudah baik",
                "Lakukan aktivitas yang Anda nikmati secara rutin",
                "Jaga hubungan sosial yang positif",
                "Praktikkan mindfulness atau meditasi",
                "Lakukan skrining berkala untuk monitoring"
            ];
            iconHTML = `<svg class="w-12 h-12 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>`;
            bgClass = 'bg-green-500/20 border border-green-400';
        }

        // Update result display
        resultIconEl.innerHTML = iconHTML;
        resultIconEl.className = `w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center ${bgClass}`;
        interpretationEl.textContent = interpretation;
        recommendationEl.textContent = recommendation;
        
        // Update steps
        stepsListEl.innerHTML = steps.map(step => `<li class="text-left">• ${step}</li>`).join('');

        // Store result data
        currentResult.status = status;
        currentResult.interpretation = interpretation;
        currentResult.recommendation = recommendation;

        // Show result section
        formSection.classList.add('hidden');
        resultSection.classList.remove('hidden');
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // Handle save result
    saveResultBtn.addEventListener('click', async () => {
        if (!currentResult) return;

        try {
            saveResultBtn.disabled = true;
            saveResultBtn.textContent = 'Menyimpan...';

            // Prepare data for API
            const assessmentData = {
                answers: Object.entries(currentResult.answers).map(([key, value]) => ({
                    question_number: parseInt(key.replace('q', '')),
                    answer: value === 1
                })),
                completion_time: Math.floor(Math.random() * 300) + 60, // Simulate completion time
                total_score: currentResult.score
            };

            // Real API call
            const result = await Utils.apiCall('/assessments', {
                method: 'POST',
                body: JSON.stringify(assessmentData)
            });

            Utils.showNotification('Hasil berhasil disimpan!', 'success');

            // Redirect to dashboard after short delay
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 2000);

        } catch (error) {
            console.error('Error saving result:', error);
            Utils.showNotification('Gagal menyimpan hasil. Coba lagi nanti.', 'error');
        } finally {
            saveResultBtn.disabled = false;
            saveResultBtn.textContent = 'Simpan Hasil';
        }
    });

    // Handle reset button
    resetButton.addEventListener('click', () => {
        if (confirm('Apakah Anda yakin ingin mengulang skrining? Data yang sudah diisi akan hilang.')) {
            srqForm.reset();
            currentResult = null;
            resultSection.classList.add('hidden');
            formSection.classList.remove('hidden');
            alertMessage.classList.add('hidden');
            updateProgress();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    });

    // Initialize
    renderQuestions();
    updateProgress();
});
