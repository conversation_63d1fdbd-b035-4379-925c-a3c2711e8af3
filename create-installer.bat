@echo off
echo ========================================
echo SantriMental Module Installer Creator
echo ========================================
echo.

set "MODULES_DIR=%cd%\santrimental-modules"
set "INSTALLER_DIR=%cd%\santrimental-installer"

if not exist "%MODULES_DIR%" (
    echo ERROR: Modules not extracted yet!
    echo Please run extract-modules.bat first
    pause
    exit /b 1
)

echo Creating installer package...
if exist "%INSTALLER_DIR%" rmdir /s /q "%INSTALLER_DIR%"
mkdir "%INSTALLER_DIR%"

echo.
echo [1/5] Copying extracted modules...
xcopy "%MODULES_DIR%" "%INSTALLER_DIR%\modules" /E /I /H /Y

echo [2/5] Creating installation script...
(
echo @echo off
echo echo ========================================
echo echo SantriMental Module Installer
echo echo ========================================
echo echo.
echo.
echo REM Check if we're in a Laravel project
echo if not exist "artisan" ^(
echo     echo ERROR: This must be run in a Laravel project root directory!
echo     echo Please run: composer create-project laravel/laravel santrimental
echo     echo Then cd santrimental and run this installer
echo     pause
echo     exit /b 1
echo ^)
echo.
echo echo Installing SantriMental modules...
echo echo.
echo.
echo echo [1/8] Installing Views...
echo xcopy "modules\resources\views\*" "resources\views\" /Y
echo.
echo echo [2/8] Installing JavaScript...
echo if not exist "public\js" mkdir "public\js"
echo xcopy "modules\public\js\*" "public\js\" /Y
echo.
echo echo [3/8] Installing CSS...
echo if not exist "public\css" mkdir "public\css"
echo xcopy "modules\public\css\*" "public\css\" /Y
echo.
echo echo [4/8] Installing Controllers...
echo xcopy "modules\app\Http\Controllers\*" "app\Http\Controllers\" /E /Y
echo.
echo echo [5/8] Installing Models...
echo xcopy "modules\app\Models\*" "app\Models\" /Y
echo.
echo echo [6/8] Installing Migrations...
echo xcopy "modules\database\migrations\*" "database\migrations\" /Y
echo.
echo echo [7/8] Installing Seeders...
echo xcopy "modules\database\seeders\*" "database\seeders\" /Y
echo.
echo echo [8/8] Installing Routes and Middleware...
echo copy "modules\routes\web.php" "routes\web.php" /Y
echo copy "modules\routes\api.php" "routes\api.php" /Y
echo xcopy "modules\app\Http\Middleware\*" "app\Http\Middleware\" /Y
echo.
echo if exist "modules\app\Modules" ^(
echo     echo Installing Modular Structure...
echo     xcopy "modules\app\Modules\*" "app\Modules\" /E /Y
echo ^)
echo.
echo echo.
echo echo ========================================
echo echo Installation Complete!
echo echo ========================================
echo echo.
echo echo Next steps:
echo echo 1. Update your .env file
echo echo 2. Run: php artisan migrate
echo echo 3. Run: php artisan db:seed
echo echo 4. Run: php artisan serve
echo echo.
echo echo Your SantriMental application is ready!
echo echo.
echo pause
) > "%INSTALLER_DIR%\install.bat"

echo [3/5] Creating Laravel setup guide...
(
echo # SantriMental Module Installation Guide
echo.
echo ## Prerequisites
echo - PHP ^>= 8.1
echo - Composer
echo - Node.js ^>= 16
echo - MySQL/PostgreSQL
echo.
echo ## Fresh Laravel Installation
echo.
echo ### Step 1: Create Fresh Laravel Project
echo ```bash
echo composer create-project laravel/laravel santrimental
echo cd santrimental
echo ```
echo.
echo ### Step 2: Install SantriMental Modules
echo 1. Extract santrimental-installer.zip to your Laravel project root
echo 2. Run the installer:
echo ```bash
echo install.bat
echo ```
echo.
echo ### Step 3: Configure Environment
echo Update your `.env` file:
echo ```env
echo APP_NAME=SantriMental
echo APP_URL=http://localhost:8000
echo.
echo DB_CONNECTION=mysql
echo DB_HOST=127.0.0.1
echo DB_PORT=3306
echo DB_DATABASE=santrimental
echo DB_USERNAME=root
echo DB_PASSWORD=
echo ```
echo.
echo ### Step 4: Setup Database
echo ```bash
echo php artisan migrate
echo php artisan db:seed
echo ```
echo.
echo ### Step 5: Install Dependencies
echo ```bash
echo composer install
echo npm install
echo npm run build
echo ```
echo.
echo ### Step 6: Run Application
echo ```bash
echo php artisan serve
echo ```
echo.
echo ## Module Structure
echo.
echo ### Frontend Modules
echo - **Views**: Modern Blade templates with glass morphism design
echo - **JavaScript**: Modular JS framework with modern components
echo - **CSS**: Complete design system with dark/light theme
echo.
echo ### Backend Modules
echo - **Controllers**: API controllers for all features
echo - **Models**: Eloquent models with relationships
echo - **Migrations**: Database schema for mental health platform
echo - **Seeders**: Sample data for testing
echo - **Middleware**: Role-based access control
echo.
echo ### Features Included
echo - Multi-role dashboard ^(Admin, Guru, Siswa, Orang Tua^)
echo - Dynamic form system
echo - Mental health assessments ^(SRQ-20^)
echo - Modern responsive UI
echo - Authentication system
echo - Role-based permissions
echo.
echo ## URLs After Installation
echo - Home: http://localhost:8000
echo - Student Dashboard: http://localhost:8000/dashboard
echo - Admin Dashboard: http://localhost:8000/admin/dashboard
echo - Teacher Dashboard: http://localhost:8000/guru/dashboard
echo - Parent Dashboard: http://localhost:8000/orangtua/dashboard
echo.
echo ## Troubleshooting
echo.
echo ### Common Issues
echo 1. **Permission errors**: Run as administrator
echo 2. **Database errors**: Check .env configuration
echo 3. **Asset errors**: Run `npm run build`
echo 4. **Route errors**: Clear cache with `php artisan route:clear`
echo.
echo ### Support
echo For issues or questions, check the documentation in the DOCS folder.
) > "%INSTALLER_DIR%\README.md"

echo [4/5] Creating package configuration...
(
echo {
echo   "name": "santrimental-modules",
echo   "version": "1.0.0",
echo   "description": "SantriMental Mental Health Assessment Platform Modules",
echo   "modules": [
echo     "Frontend Views",
echo     "JavaScript Framework", 
echo     "CSS Design System",
echo     "API Controllers",
echo     "Database Models",
echo     "Migrations",
echo     "Seeders",
echo     "Authentication",
echo     "Role Management"
echo   ],
echo   "requirements": {
echo     "php": "^8.1",
echo     "laravel": "^10.0",
echo     "mysql": "^8.0"
echo   },
echo   "features": [
echo     "Multi-role dashboards",
echo     "Dynamic form system", 
echo     "Mental health assessments",
echo     "Modern responsive UI",
echo     "Role-based permissions",
echo     "Glass morphism design",
echo     "Dark/Light theme support"
echo   ]
echo }
) > "%INSTALLER_DIR%\package.json"

echo [5/5] Creating environment template...
(
echo APP_NAME=SantriMental
echo APP_ENV=local
echo APP_KEY=
echo APP_DEBUG=true
echo APP_URL=http://localhost:8000
echo.
echo LOG_CHANNEL=stack
echo LOG_DEPRECATIONS_CHANNEL=null
echo LOG_LEVEL=debug
echo.
echo DB_CONNECTION=mysql
echo DB_HOST=127.0.0.1
echo DB_PORT=3306
echo DB_DATABASE=santrimental
echo DB_USERNAME=root
echo DB_PASSWORD=
echo.
echo BROADCAST_DRIVER=log
echo CACHE_DRIVER=file
echo FILESYSTEM_DISK=local
echo QUEUE_CONNECTION=sync
echo SESSION_DRIVER=file
echo SESSION_LIFETIME=120
echo.
echo MEMCACHED_HOST=127.0.0.1
echo.
echo REDIS_HOST=127.0.0.1
echo REDIS_PASSWORD=null
echo REDIS_PORT=6379
echo.
echo MAIL_MAILER=smtp
echo MAIL_HOST=mailpit
echo MAIL_PORT=1025
echo MAIL_USERNAME=null
echo MAIL_PASSWORD=null
echo MAIL_ENCRYPTION=null
echo MAIL_FROM_ADDRESS="<EMAIL>"
echo MAIL_FROM_NAME="${APP_NAME}"
echo.
echo # SantriMental Specific Configuration
echo FRONTEND_URL=http://localhost:8000
echo SESSION_DOMAIN=localhost
echo SANCTUM_STATEFUL_DOMAINS=localhost
) > "%INSTALLER_DIR%\.env.example"

echo.
echo ========================================
echo Installer package created!
echo ========================================
echo.
echo Package location: %INSTALLER_DIR%
echo.
echo Contents:
echo - modules/           ^(All SantriMental modules^)
echo - install.bat        ^(Installation script^)
echo - README.md          ^(Installation guide^)
echo - package.json       ^(Module information^)
echo - .env.example       ^(Environment template^)
echo.
echo To distribute: Zip the installer directory
echo.
pause
