<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessment_responses', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->uuid('form_id');
            $table->json('answers')->nullable();
            $table->json('score_data')->nullable();
            $table->json('interpretation')->nullable();
            $table->enum('status', ['started', 'in_progress', 'completed', 'abandoned'])->default('started');
            $table->integer('completion_time')->nullable()->comment('Completion time in seconds');
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();

            $table->foreign('form_id')->references('id')->on('assessment_forms')->onDelete('cascade');

            $table->index(['user_id', 'form_id']);
            $table->index(['status', 'completed_at']);
            $table->index('started_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessment_responses');
    }
};
