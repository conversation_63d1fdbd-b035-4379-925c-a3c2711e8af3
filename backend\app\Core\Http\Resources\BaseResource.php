<?php

namespace App\Core\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

abstract class BaseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }

    /**
     * Get formatted date
     */
    protected function formatDate($date, string $format = 'Y-m-d H:i:s'): ?string
    {
        return $date ? $date->format($format) : null;
    }

    /**
     * Get human readable date
     */
    protected function humanDate($date): ?string
    {
        return $date ? $date->diffForHumans() : null;
    }



    /**
     * Include data when condition is true
     */
    protected function whenCondition($condition, $value, $default = null)
    {
        return $condition ? $value : ($default ?? $this->missingValue());
    }





    /**
     * Include data when user is authenticated
     */
    protected function whenAuth($value)
    {
        return auth()->check() ? $value : $this->missingValue();
    }

    /**
     * Include data when user is guest
     */
    protected function whenGuest($value)
    {
        return auth()->guest() ? $value : $this->missingValue();
    }

    /**
     * Get file URL
     */
    protected function fileUrl(?string $path): ?string
    {
        return $path ? asset('storage/' . $path) : null;
    }

    /**
     * Get avatar URL with fallback
     */
    protected function avatarUrl(?string $path, string $name = ''): string
    {
        if ($path) {
            return $this->fileUrl($path);
        }

        // Generate avatar using initials
        $initials = collect(explode(' ', $name))
            ->map(fn($word) => strtoupper(substr($word, 0, 1)))
            ->take(2)
            ->implode('');

        return "https://ui-avatars.com/api/?name={$initials}&background=6366f1&color=ffffff&size=128";
    }

    /**
     * Get status badge data
     */
    protected function statusBadge(bool $isActive): array
    {
        return [
            'status' => $isActive ? 'active' : 'inactive',
            'label' => $isActive ? 'Active' : 'Inactive',
            'color' => $isActive ? 'green' : 'gray',
        ];
    }

    /**
     * Get truncated text
     */
    protected function truncate(?string $text, int $length = 100): ?string
    {
        if (!$text) {
            return null;
        }

        return strlen($text) > $length 
            ? substr($text, 0, $length) . '...' 
            : $text;
    }

    /**
     * Get additional metadata
     */
    protected function getMetadata(): array
    {
        return [
            'created_at_human' => $this->humanDate($this->created_at),
            'updated_at_human' => $this->humanDate($this->updated_at),
        ];
    }
}
