# Simple SantriMental Application Test
$baseUrl = "http://127.0.0.1:8000/api"
$headers = @{
    "Accept" = "application/json"
    "Content-Type" = "application/json"
}

Write-Host "=== SANTRIMENTAL APPLICATION TEST ===" -ForegroundColor Green

# Test 1: Health Check
Write-Host "`n1. Testing Application Health" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:8000" -Method GET
    Write-Host "SUCCESS: Application Status $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: User Registration
Write-Host "`n2. Testing User Registration" -ForegroundColor Yellow
try {
    $registerData = @{
        first_name = "Test"
        last_name = "User"
        email = "<EMAIL>"
        password = "Password123!"
        password_confirmation = "Password123!"
        role = "student"
        terms_accepted = $true
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "$baseUrl/auth/register" -Method POST -Headers $headers -Body $registerData
    Write-Host "SUCCESS: Registration Status $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "User registered: $($data.data.user.full_name)" -ForegroundColor Green
    $userToken = $data.data.token
} catch {
    Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
    $userToken = $null
}

# Test 3: User Login
Write-Host "`n3. Testing User Login" -ForegroundColor Yellow
try {
    $loginData = @{
        login = "<EMAIL>"
        password = "Password123!"
        device_name = "Test Device"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "$baseUrl/auth/login" -Method POST -Headers $headers -Body $loginData
    Write-Host "SUCCESS: Login Status $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    $loginToken = $data.data.token
} catch {
    Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
    $loginToken = $userToken
}

# Test 4: Get Available Forms
Write-Host "`n4. Testing Assessment Forms" -ForegroundColor Yellow
try {
    $authHeaders = $headers.Clone()
    $authHeaders["Authorization"] = "Bearer $loginToken"
    
    $response = Invoke-WebRequest -Uri "$baseUrl/assessments/available" -Method GET -Headers $authHeaders
    Write-Host "SUCCESS: Available Forms Status $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "Available forms: $($data.data.Count)" -ForegroundColor Green
} catch {
    Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== TEST COMPLETE ===" -ForegroundColor Green
Write-Host "Server running at: http://127.0.0.1:8000" -ForegroundColor Yellow
