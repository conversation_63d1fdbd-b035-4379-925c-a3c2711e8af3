<?php

namespace App\Modules\Assessment\Http\Controllers\Api;

use App\Core\Http\Controllers\BaseApiController;
use App\Modules\Assessment\Http\Requests\StartAssessmentRequest;
use App\Modules\Assessment\Http\Requests\SubmitAssessmentRequest;
use App\Modules\Assessment\Http\Requests\SaveProgressRequest;
use App\Modules\Assessment\Http\Resources\AssessmentFormResource;
use App\Modules\Assessment\Http\Resources\AssessmentResponseResource;
use App\Modules\Assessment\Services\AssessmentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Assessment Management
 * 
 * APIs for managing assessments and responses
 */
class AssessmentController extends BaseApiController
{
    public function __construct(
        private AssessmentService $assessmentService
    ) {
        $this->middleware('auth:sanctum');
    }

    /**
     * Get available assessment forms
     * 
     * @queryParam category string Filter by category
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $category = $request->get('category');
            
            if ($category) {
                $forms = $this->assessmentService->getFormsByCategory($category);
            } else {
                $forms = $this->assessmentService->getAvailableForms();
            }

            return $this->collectionResponse(
                AssessmentFormResource::collection($forms),
                'Assessment forms retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get available forms for authenticated user
     */
    public function available(): JsonResponse
    {
        try {
            $userId = auth()->id();
            $forms = $this->assessmentService->getAvailableFormsForUser($userId);

            return $this->collectionResponse(
                AssessmentFormResource::collection($forms),
                'Available assessment forms retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get specific assessment form by code
     */
    public function show(string $code): JsonResponse
    {
        try {
            $form = $this->assessmentService->getFormByCode($code);

            if (!$form) {
                return $this->notFoundResponse('Assessment form not found');
            }

            return $this->resourceResponse(
                new AssessmentFormResource($form),
                'Assessment form retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Start an assessment
     */
    public function start(StartAssessmentRequest $request): JsonResponse
    {
        try {
            $userId = auth()->id();
            $formCode = $request->validated()['form_code'];

            $response = $this->assessmentService->startAssessment($userId, $formCode);

            return $this->createdResponse(
                new AssessmentResponseResource($response),
                'Assessment started successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Submit assessment answers
     */
    public function submit(SubmitAssessmentRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $responseId = $data['response_id'];
            $answers = $data['answers'];

            $response = $this->assessmentService->submitAssessment($responseId, $answers);

            return $this->updatedResponse(
                new AssessmentResponseResource($response),
                'Assessment submitted successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Save assessment progress
     */
    public function saveProgress(SaveProgressRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $responseId = $data['response_id'];
            $answers = $data['answers'];

            $response = $this->assessmentService->saveProgress($responseId, $answers);

            return $this->updatedResponse(
                new AssessmentResponseResource($response),
                'Assessment progress saved successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get user's assessment history
     */
    public function history(): JsonResponse
    {
        try {
            $userId = auth()->id();
            $history = $this->assessmentService->getUserHistory($userId);

            return $this->collectionResponse(
                AssessmentResponseResource::collection($history),
                'Assessment history retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get user's assessment statistics
     */
    public function statistics(): JsonResponse
    {
        try {
            $userId = auth()->id();
            $statistics = $this->assessmentService->getUserStatistics($userId);

            return $this->successResponse(
                $statistics,
                'Assessment statistics retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get overall assessment statistics (admin only)
     */
    public function overallStatistics(): JsonResponse
    {
        try {
            // Check if user is admin
            if (!auth()->user()->hasRole('admin')) {
                return $this->forbiddenResponse('Access denied');
            }

            $statistics = $this->assessmentService->getOverallStatistics();

            return $this->successResponse(
                $statistics,
                'Overall assessment statistics retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * Get assessment categories
     */
    public function categories(): JsonResponse
    {
        try {
            $categories = [
                'mental_health' => 'Mental Health',
                'self_efficacy' => 'Self Efficacy',
                'self_care' => 'Self Care',
                'knowledge' => 'Knowledge',
                'discrimination' => 'Discrimination',
            ];

            return $this->successResponse(
                $categories,
                'Assessment categories retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }
}
