<?php

namespace App\Modules\Dashboard\Services;

interface DashboardServiceInterface
{
    /**
     * Get complete dashboard data for a user
     */
    public function getDashboardData(int $userId, string $role): array;

    /**
     * Get dashboard statistics
     */
    public function getStatistics(int $userId, string $role): array;

    /**
     * Get dashboard charts data
     */
    public function getChartsData(int $userId, string $role): array;

    /**
     * Get recent activities
     */
    public function getRecentActivities(int $userId, int $limit = 10): array;

    /**
     * Get role-specific dashboard widgets
     */
    public function getRoleWidgets(string $role): array;

    /**
     * Cache dashboard data
     */
    public function cacheDashboardData(int $userId, string $role, array $data): void;

    /**
     * Get cached dashboard data
     */
    public function getCachedDashboardData(int $userId, string $role): ?array;

    /**
     * Clear dashboard cache
     */
    public function clearDashboardCache(int $userId): void;
}
