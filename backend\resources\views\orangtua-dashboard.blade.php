<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Dashboard Orang Tua - SantriMental</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    
    <!-- Navigation Header -->
    <nav class="glass-card p-4 mb-6">
        <div class="flex items-center justify-between max-w-7xl mx-auto">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-gradient-to-r from-pink-500 to-red-500 rounded-lg flex items-center justify-center mr-3">
                    <span class="text-white font-bold text-sm">O</span>
                </div>
                <h1 class="text-xl font-bold text-white">Dashboard Orang Tua</h1>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-purple-200 text-sm" id="parent-name">Orang Tua</span>
                <button onclick="logout()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                    Logout
                </button>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4">
        
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="glass-card p-6 rounded-xl">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-200 text-sm">Jumlah Anak</p>
                        <p id="total-children" class="text-2xl font-bold text-white">-</p>
                    </div>
                    <div class="w-12 h-12 bg-pink-500/20 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">👶</span>
                    </div>
                </div>
            </div>

            <div class="glass-card p-6 rounded-xl">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-200 text-sm">Assessment Bulan Ini</p>
                        <p id="monthly-assessments" class="text-2xl font-bold text-white">-</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">📋</span>
                    </div>
                </div>
            </div>

            <div class="glass-card p-6 rounded-xl">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-200 text-sm">Assessment Terakhir</p>
                        <p id="latest-assessment" class="text-sm font-medium text-white">-</p>
                    </div>
                    <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">📈</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Children Cards -->
        <div class="mb-8">
            <h3 class="text-lg font-semibold text-white mb-4">Anak-anak Saya</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="children-grid">
                <div class="text-center text-purple-200 py-8">Memuat data anak...</div>
            </div>
        </div>

        <!-- Recent Assessments -->
        <div class="glass-card p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-white mb-4">Riwayat Assessment Anak</h3>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-white/20">
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Nama Anak</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Kelas</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Assessment</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Skor</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Status</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Tanggal</th>
                            <th class="text-left text-purple-200 text-sm font-medium py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody id="assessments-table">
                        <tr>
                            <td colspan="7" class="text-center text-purple-200 py-8">Memuat data...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <!-- Assessment Detail Modal -->
    <div id="assessment-modal" class="hidden fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div class="glass-card p-6 rounded-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-white">Detail Assessment</h3>
                <button onclick="closeModal()" class="text-purple-200 hover:text-white">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>
            <div id="modal-content">
                <!-- Content will be inserted here -->
            </div>
        </div>
    </div>

    <script src="{{ asset('js/auth.js') }}"></script>
    <script src="{{ asset('js/utils.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const auth = window.auth;
            const Utils = window.Utils;

            // Check authentication and role
            if (!auth.isAuthenticated()) {
                window.location.href = '/';
                return;
            }

            // Load orangtua dashboard data
            try {
                const response = await Utils.apiCall('/dashboard/role-data');
                
                if (response.success) {
                    displayOrangtuaData(response.data);
                } else {
                    Utils.showNotification('Gagal memuat data dashboard', 'error');
                }
            } catch (error) {
                console.error('Failed to load orangtua data:', error);
                Utils.showNotification('Terjadi kesalahan saat memuat data', 'error');
            }

            function displayOrangtuaData(data) {
                const { stats, children, assessments } = data;

                // Update stats
                document.getElementById('total-children').textContent = stats.total_children || 0;
                document.getElementById('monthly-assessments').textContent = stats.assessments_this_month || 0;
                
                if (stats.latest_assessment) {
                    document.getElementById('latest-assessment').textContent = 
                        new Date(stats.latest_assessment.created_at).toLocaleDateString('id-ID');
                } else {
                    document.getElementById('latest-assessment').textContent = 'Belum ada';
                }

                // Display children
                displayChildren(children);

                // Display assessments
                displayAssessments(assessments);
            }

            function displayChildren(children) {
                const childrenGrid = document.getElementById('children-grid');
                
                if (children && children.length > 0) {
                    childrenGrid.innerHTML = children.map(child => `
                        <div class="glass-card p-6 rounded-xl">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-4">
                                    <span class="text-white font-bold">${child.first_name.charAt(0)}</span>
                                </div>
                                <div>
                                    <h4 class="text-white font-semibold">${child.first_name} ${child.last_name}</h4>
                                    <p class="text-purple-200 text-sm">Kelas ${child.class || '-'}</p>
                                </div>
                            </div>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-purple-200">NIS:</span>
                                    <span class="text-white">${child.student_id || '-'}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-purple-200">Gender:</span>
                                    <span class="text-white">${child.gender === 'L' ? 'Laki-laki' : child.gender === 'P' ? 'Perempuan' : '-'}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-purple-200">Status:</span>
                                    <span class="text-green-300">${child.is_active ? 'Aktif' : 'Tidak Aktif'}</span>
                                </div>
                            </div>
                        </div>
                    `).join('');
                } else {
                    childrenGrid.innerHTML = '<div class="text-center text-purple-200 py-8">Belum ada data anak</div>';
                }
            }

            function displayAssessments(assessments) {
                const tbody = document.getElementById('assessments-table');
                
                if (assessments && assessments.length > 0) {
                    tbody.innerHTML = assessments.map(assessment => {
                        const statusColors = {
                            'normal': 'bg-green-500/20 text-green-300',
                            'concern': 'bg-yellow-500/20 text-yellow-300',
                            'high_risk': 'bg-red-500/20 text-red-300',
                            'severe': 'bg-red-600/20 text-red-200'
                        };

                        return `
                            <tr class="border-b border-white/10">
                                <td class="py-3 text-white">${assessment.user.first_name} ${assessment.user.last_name}</td>
                                <td class="py-3 text-purple-200">${assessment.user.class || '-'}</td>
                                <td class="py-3 text-purple-200">${assessment.form_template.name}</td>
                                <td class="py-3 text-white">${assessment.total_score}</td>
                                <td class="py-3">
                                    <span class="px-2 py-1 rounded-full text-xs ${statusColors[assessment.status] || 'bg-gray-500/20 text-gray-300'}">
                                        ${assessment.status}
                                    </span>
                                </td>
                                <td class="py-3 text-purple-200">${new Date(assessment.created_at).toLocaleDateString('id-ID')}</td>
                                <td class="py-3">
                                    <button onclick="viewAssessmentDetail(${assessment.id})" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs transition-colors">
                                        Detail
                                    </button>
                                </td>
                            </tr>
                        `;
                    }).join('');
                } else {
                    tbody.innerHTML = '<tr><td colspan="7" class="text-center text-purple-200 py-8">Belum ada assessment</td></tr>';
                }
            }

            // Modal functions
            window.viewAssessmentDetail = (assessmentId) => {
                // This would fetch and display detailed assessment results
                document.getElementById('modal-content').innerHTML = `
                    <div class="text-center py-8">
                        <p class="text-white">Detail assessment akan ditampilkan di sini</p>
                        <p class="text-purple-200 text-sm mt-2">Assessment ID: ${assessmentId}</p>
                    </div>
                `;
                document.getElementById('assessment-modal').classList.remove('hidden');
            };

            window.closeModal = () => {
                document.getElementById('assessment-modal').classList.add('hidden');
            };

            // Logout function
            window.logout = async () => {
                try {
                    await auth.logout();
                    window.location.href = '/';
                } catch (error) {
                    console.error('Logout failed:', error);
                }
            };
        });
    </script>
</body>
</html>
