<?php

namespace App\Modules\Dashboard\Services;

use App\Modules\Dashboard\Repositories\DashboardRepositoryInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class DashboardService implements DashboardServiceInterface
{
    public function __construct(
        private DashboardRepositoryInterface $dashboardRepository
    ) {}

    /**
     * Get complete dashboard data for a user
     */
    public function getDashboardData(int $userId, string $role): array
    {
        try {
            // Try to get cached data first
            $cachedData = $this->getCachedDashboardData($userId, $role);
            if ($cachedData) {
                return $cachedData;
            }

            // Generate fresh data
            $data = [
                'statistics' => $this->getStatistics($userId, $role),
                'charts' => $this->getChartsData($userId, $role),
                'recent_activities' => $this->getRecentActivities($userId),
                'widgets' => $this->getRoleWidgets($role),
                'role_specific' => $this->dashboardRepository->getRoleSpecificData($role, $userId),
                'generated_at' => now()->toISOString(),
            ];

            // Cache the data
            $this->cacheDashboardData($userId, $role, $data);

            return $data;
        } catch (\Exception $e) {
            Log::error('Dashboard data generation failed', [
                'user_id' => $userId,
                'role' => $role,
                'error' => $e->getMessage(),
            ]);

            // Return mock data as fallback
            return $this->getMockDashboardData($role);
        }
    }

    /**
     * Get dashboard statistics
     */
    public function getStatistics(int $userId, string $role): array
    {
        $stats = $this->dashboardRepository->getDashboardStats($role, $userId);
        $monthlyStats = $this->dashboardRepository->getMonthlyStats($userId, $role);
        $assessmentSummary = $this->dashboardRepository->getAssessmentSummary($userId, $role);

        return array_merge($stats, [
            'monthly_stats' => $monthlyStats,
            'assessment_summary' => $assessmentSummary,
        ]);
    }

    /**
     * Get dashboard charts data
     */
    public function getChartsData(int $userId, string $role): array
    {
        return [
            'trend_data' => $this->dashboardRepository->getTrendData($role, $userId, '6months'),
            'monthly_distribution' => $this->dashboardRepository->getMonthlyStats($userId, $role),
        ];
    }

    /**
     * Get recent activities
     */
    public function getRecentActivities(int $userId, int $limit = 10): array
    {
        return $this->dashboardRepository->getRecentActivities($userId, $limit);
    }

    /**
     * Get role-specific dashboard widgets
     */
    public function getRoleWidgets(string $role): array
    {
        return match ($role) {
            'admin' => [
                'user_management',
                'system_health',
                'recent_activities',
                'assessment_overview',
                'reports',
            ],
            'guru' => [
                'student_overview',
                'class_performance',
                'assessment_alerts',
                'monthly_reports',
            ],
            'orangtua' => [
                'children_progress',
                'assessment_history',
                'recommendations',
                'upcoming_assessments',
            ],
            'siswa' => [
                'personal_stats',
                'assessment_history',
                'progress_chart',
                'recommendations',
            ],
            default => ['basic_stats'],
        };
    }

    /**
     * Cache dashboard data
     */
    public function cacheDashboardData(int $userId, string $role, array $data): void
    {
        $cacheKey = "dashboard_data_{$userId}_{$role}";
        $cacheDuration = config('dashboard.cache_duration', 300); // 5 minutes default

        Cache::put($cacheKey, $data, $cacheDuration);
    }

    /**
     * Get cached dashboard data
     */
    public function getCachedDashboardData(int $userId, string $role): ?array
    {
        $cacheKey = "dashboard_data_{$userId}_{$role}";
        return Cache::get($cacheKey);
    }

    /**
     * Clear dashboard cache
     */
    public function clearDashboardCache(int $userId): void
    {
        $roles = ['admin', 'guru', 'orangtua', 'siswa'];
        
        foreach ($roles as $role) {
            $cacheKey = "dashboard_data_{$userId}_{$role}";
            Cache::forget($cacheKey);
        }
    }

    /**
     * Get mock dashboard data as fallback
     */
    private function getMockDashboardData(string $role): array
    {
        $mockData = [
            'admin' => [
                'statistics' => [
                    'total_users' => 156,
                    'total_siswa' => 120,
                    'total_guru' => 25,
                    'total_orangtua' => 11,
                    'total_assessments' => 450,
                    'monthly_assessments' => 89,
                ],
                'charts' => [
                    'trend_data' => [
                        ['month' => 'Jan 2024', 'average_score' => 3.2, 'total_assessments' => 45],
                        ['month' => 'Feb 2024', 'average_score' => 3.5, 'total_assessments' => 52],
                        ['month' => 'Mar 2024', 'average_score' => 3.1, 'total_assessments' => 48],
                    ],
                ],
                'recent_activities' => [],
                'widgets' => $this->getRoleWidgets('admin'),
            ],
            'guru' => [
                'statistics' => [
                    'total_students' => 32,
                    'monthly_assessments' => 18,
                    'students_need_attention' => 3,
                    'average_class_score' => 3.2,
                ],
                'charts' => [
                    'trend_data' => [
                        ['month' => 'Jan 2024', 'average_score' => 3.1, 'total_assessments' => 15],
                        ['month' => 'Feb 2024', 'average_score' => 3.3, 'total_assessments' => 18],
                        ['month' => 'Mar 2024', 'average_score' => 3.2, 'total_assessments' => 16],
                    ],
                ],
                'recent_activities' => [],
                'widgets' => $this->getRoleWidgets('guru'),
            ],
            'orangtua' => [
                'statistics' => [
                    'total_children' => 2,
                    'children_assessments' => 8,
                    'children_need_attention' => 0,
                    'latest_child_score' => 3.5,
                ],
                'charts' => [
                    'trend_data' => [
                        ['month' => 'Jan 2024', 'average_score' => 3.4, 'total_assessments' => 2],
                        ['month' => 'Feb 2024', 'average_score' => 3.6, 'total_assessments' => 3],
                        ['month' => 'Mar 2024', 'average_score' => 3.5, 'total_assessments' => 3],
                    ],
                ],
                'recent_activities' => [],
                'widgets' => $this->getRoleWidgets('orangtua'),
            ],
            'siswa' => [
                'statistics' => [
                    'total_assessments' => 15,
                    'latest_score' => 4,
                    'latest_status' => 'normal',
                    'monthly_count' => 5,
                ],
                'charts' => [
                    'trend_data' => [
                        ['month' => 'Jan 2024', 'average_score' => 3, 'total_assessments' => 3],
                        ['month' => 'Feb 2024', 'average_score' => 5, 'total_assessments' => 4],
                        ['month' => 'Mar 2024', 'average_score' => 4, 'total_assessments' => 5],
                    ],
                ],
                'recent_activities' => [],
                'widgets' => $this->getRoleWidgets('siswa'),
            ],
        ];

        return $mockData[$role] ?? $mockData['siswa'];
    }
}
