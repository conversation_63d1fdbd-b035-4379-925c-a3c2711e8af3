<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Assessment;
use App\Models\AssessmentAnswer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AssessmentController extends Controller
{
    /**
     * Get user's assessment history.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $assessments = $request->user()
            ->assessments()
            ->with('answers')
            ->latest()
            ->paginate(10);

        return response()->json($assessments);
    }

    /**
     * Store a new assessment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'answers' => ['required', 'array', 'size:20'],
            'answers.*.question_number' => ['required', 'integer', 'between:1,20'],
            'answers.*.answer' => ['required', 'boolean'],
            'completion_time' => ['required', 'integer', 'min:1'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Validate answers completeness
        if (!AssessmentAnswer::validateAnswers($request->answers)) {
            return response()->json([
                'message' => 'Invalid or incomplete answers'
            ], 422);
        }

        try {
            DB::beginTransaction();

            // Calculate total score (sum of 'yes' answers)
            $totalScore = collect($request->answers)
                ->sum(function ($answer) {
                    return $answer['answer'] ? 1 : 0;
                });

            // Create assessment
            $assessment = $request->user()->assessments()->create([
                'total_score' => $totalScore,
                'completion_time' => $request->completion_time,
                'status' => $totalScore > 6 ? 'concern' : 'normal',
            ]);

            // Create answers
            $assessment->answers()->createMany($request->answers);

            DB::commit();

            return response()->json([
                'message' => 'Assessment submitted successfully',
                'assessment' => $assessment->load('answers'),
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Failed to submit assessment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific assessment.
     *
     * @param  \App\Models\Assessment  $assessment
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Assessment $assessment)
    {
        // Check if the assessment belongs to the authenticated user
        if ($assessment->user_id !== auth()->id()) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        return response()->json([
            'assessment' => $assessment->load('answers'),
            'summary' => AssessmentAnswer::getAnswersSummary($assessment->id),
        ]);
    }

    /**
     * Get user's dashboard statistics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function dashboardStats(Request $request)
    {
        $user = $request->user();
        
        // Get total assessments
        $totalAssessments = $user->assessments()->count();
        
        // Get latest assessment
        $latestAssessment = $user->latestAssessment;
        
        // Get monthly stats
        $monthlyStats = $user->getMonthlyStats();
        
        // Get assessment trend
        $trend = Assessment::getTrend();
        
        // Get monthly distribution
        $distribution = Assessment::getMonthlyDistribution();

        return response()->json([
            'total_assessments' => $totalAssessments,
            'latest_assessment' => $latestAssessment,
            'monthly_stats' => $monthlyStats,
            'trend' => $trend,
            'distribution' => $distribution,
        ]);
    }

    /**
     * Get assessment questions.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function questions()
    {
        return response()->json([
            'questions' => AssessmentAnswer::getQuestions(),
        ]);
    }

    /**
     * Get monthly report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function monthlyReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'month' => ['required', 'date_format:Y-m'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $date = \Carbon\Carbon::createFromFormat('Y-m', $request->month);

        $report = $request->user()
            ->assessments()
            ->whereYear('created_at', $date->year)
            ->whereMonth('created_at', $date->month)
            ->with('answers')
            ->get()
            ->map(function ($assessment) {
                return [
                    'id' => $assessment->id,
                    'date' => $assessment->created_at->format('Y-m-d'),
                    'score' => $assessment->total_score,
                    'status' => $assessment->status,
                    'completion_time' => $assessment->completion_time,
                    'answers_summary' => AssessmentAnswer::getAnswersSummary($assessment->id),
                ];
            });

        return response()->json([
            'month' => $request->month,
            'assessments' => $report,
            'total' => $report->count(),
            'average_score' => $report->avg('score'),
            'concerns' => $report->where('status', 'concern')->count(),
        ]);
    }
}
