<?php

namespace App\Modules\Assessment\Http\Resources;

use App\Core\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class AssessmentFormResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'name' => $this->name,
            'description' => $this->description,
            'category' => $this->category,
            'category_label' => $this->getCategoryLabel(),
            'questions_count' => $this->questions_count,
            'estimated_time' => $this->estimated_time,
            'time_limit' => $this->time_limit,
            'is_active' => $this->is_active,
            'status_badge' => $this->status_badge,
            
            // Include questions only when specifically requested
            'questions' => $this->when(
                $request->has('include_questions') || $request->routeIs('*.show'),
                $this->questions
            ),
            
            // Include scoring rules for authenticated users
            'scoring_rules' => $this->when(
                auth()->check(),
                $this->scoring_rules
            ),

            // Include interpretation rules for authenticated users
            'interpretation_rules' => $this->when(
                auth()->check(),
                $this->interpretation_rules
            ),
            
            // Statistics
            'statistics' => $this->when(
                $request->has('include_statistics'),
                function () {
                    return [
                        'total_responses' => $this->responses_count ?? 0,
                        'completed_responses' => $this->completed_responses_count ?? 0,
                    ];
                }
            ),
            
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            'formatted_created_at' => $this->formatted_created_at,
            'created_at_human' => $this->created_at_human,
        ];
    }

    /**
     * Get category label
     */
    private function getCategoryLabel(): string
    {
        $categories = [
            'mental_health' => 'Mental Health',
            'self_efficacy' => 'Self Efficacy',
            'self_care' => 'Self Care',
            'knowledge' => 'Knowledge',
            'discrimination' => 'Discrimination',
        ];

        return $categories[$this->category] ?? $this->category;
    }
}
