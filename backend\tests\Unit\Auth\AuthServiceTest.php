<?php

namespace Tests\Unit\Auth;

use App\Models\Role;
use App\Models\User;
use App\Modules\Auth\Services\AuthService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AuthServiceTest extends TestCase
{
    use RefreshDatabase;

    protected AuthService $authService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->authService = new AuthService();
        
        // Create roles
        Role::create(['name' => 'admin', 'display_name' => 'Administrator', 'permissions' => '[]']);
        Role::create(['name' => 'guru', 'display_name' => 'Guru', 'permissions' => '[]']);
        Role::create(['name' => 'orangtua', 'display_name' => 'Orang Tua', 'permissions' => '[]']);
        Role::create(['name' => 'siswa', 'display_name' => 'Siswa', 'permissions' => '[]']);
    }

    public function test_register_creates_user_with_correct_data()
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'phone' => '081234567890',
            'student_id' => 'STD001',
            'class' => '10A',
            'grade' => 10,
            'role' => 'siswa'
        ];

        $result = $this->authService->register($userData);

        $this->assertArrayHasKey('user', $result);
        $this->assertArrayHasKey('token', $result);
        $this->assertArrayHasKey('expires_at', $result);

        $user = $result['user'];
        $this->assertEquals('John', $user->first_name);
        $this->assertEquals('Doe', $user->last_name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('siswa', $user->role->name);
        $this->assertTrue(Hash::check('password123', $user->password));
    }

    public function test_register_throws_exception_for_invalid_role()
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'invalid_role'
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid role specified');

        $this->authService->register($userData);
    }

    public function test_login_with_valid_email_credentials()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $role->id,
            'is_active' => true
        ]);

        $credentials = [
            'login' => '<EMAIL>',
            'password' => 'password123',
            'device_name' => 'Test Device'
        ];

        $result = $this->authService->login($credentials);

        $this->assertNotNull($result);
        $this->assertArrayHasKey('user', $result);
        $this->assertArrayHasKey('token', $result);
        $this->assertArrayHasKey('expires_at', $result);
        $this->assertEquals($user->id, $result['user']->id);
    }

    public function test_login_with_valid_student_id_credentials()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'student_id' => 'STD001',
            'password' => Hash::make('password123'),
            'role_id' => $role->id,
            'is_active' => true
        ]);

        $credentials = [
            'login' => 'STD001',
            'password' => 'password123',
            'device_name' => 'Test Device'
        ];

        $result = $this->authService->login($credentials);

        $this->assertNotNull($result);
        $this->assertEquals($user->id, $result['user']->id);
    }

    public function test_login_returns_null_for_invalid_credentials()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $role->id,
            'is_active' => true
        ]);

        $credentials = [
            'login' => '<EMAIL>',
            'password' => 'wrongpassword',
            'device_name' => 'Test Device'
        ];

        $result = $this->authService->login($credentials);

        $this->assertNull($result);
    }

    public function test_login_throws_exception_for_inactive_user()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $role->id,
            'is_active' => false
        ]);

        $credentials = [
            'login' => '<EMAIL>',
            'password' => 'password123',
            'device_name' => 'Test Device'
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Account is deactivated');

        $this->authService->login($credentials);
    }

    public function test_logout_revokes_current_token()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $role->id,
            'is_active' => true
        ]);

        $token = $user->createToken('test-token');
        $user->withAccessToken($token->accessToken);

        $this->authService->logout($user);

        $this->assertDatabaseMissing('personal_access_tokens', [
            'id' => $token->accessToken->id
        ]);
    }

    public function test_change_password_with_correct_current_password()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('oldpassword'),
            'role_id' => $role->id,
            'is_active' => true
        ]);

        $result = $this->authService->changePassword($user, 'oldpassword', 'newpassword');

        $this->assertTrue($result);
        $user->refresh();
        $this->assertTrue(Hash::check('newpassword', $user->password));
    }

    public function test_change_password_fails_with_incorrect_current_password()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('oldpassword'),
            'role_id' => $role->id,
            'is_active' => true
        ]);

        $result = $this->authService->changePassword($user, 'wrongpassword', 'newpassword');

        $this->assertFalse($result);
        $user->refresh();
        $this->assertTrue(Hash::check('oldpassword', $user->password));
    }

    public function test_get_authenticated_user_loads_relationships()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $role->id,
            'is_active' => true
        ]);

        $result = $this->authService->getAuthenticatedUser($user);

        $this->assertTrue($result->relationLoaded('role'));
        $this->assertTrue($result->relationLoaded('children'));
        $this->assertTrue($result->relationLoaded('parents'));
        $this->assertTrue($result->relationLoaded('teacher'));
        $this->assertTrue($result->relationLoaded('students'));
    }

    public function test_refresh_token_creates_new_token_and_revokes_old()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $role->id,
            'is_active' => true
        ]);

        $oldToken = $user->createToken('test-token');
        $user->withAccessToken($oldToken->accessToken);

        $result = $this->authService->refreshToken($user);

        $this->assertArrayHasKey('token', $result);
        $this->assertArrayHasKey('expires_at', $result);

        // Old token should be revoked
        $this->assertDatabaseMissing('personal_access_tokens', [
            'id' => $oldToken->accessToken->id
        ]);

        // New token should exist
        $this->assertDatabaseHas('personal_access_tokens', [
            'tokenable_id' => $user->id,
            'name' => 'test-token'
        ]);
    }
}
