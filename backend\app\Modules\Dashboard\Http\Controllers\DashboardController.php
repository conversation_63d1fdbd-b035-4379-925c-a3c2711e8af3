<?php

namespace App\Modules\Dashboard\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Dashboard\Services\DashboardServiceInterface;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class DashboardController extends Controller
{
    public function __construct(
        private DashboardServiceInterface $dashboardService
    ) {}

    /**
     * Get dashboard data for authenticated user
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $role = $user->roles->first()->name ?? 'siswa';

            $data = $this->dashboardService->getDashboardData($user->id, $role);

            return response()->json([
                'success' => true,
                'data' => $data,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $role,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load dashboard data',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get dashboard statistics only
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $role = $user->roles->first()->name ?? 'siswa';

            $statistics = $this->dashboardService->getStatistics($user->id, $role);

            return response()->json([
                'success' => true,
                'data' => $statistics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load statistics',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get dashboard charts data
     */
    public function charts(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $role = $user->roles->first()->name ?? 'siswa';

            $charts = $this->dashboardService->getChartsData($user->id, $role);

            return response()->json([
                'success' => true,
                'data' => $charts,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load charts data',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get recent activities
     */
    public function activities(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $limit = $request->get('limit', 10);

            $activities = $this->dashboardService->getRecentActivities($user->id, $limit);

            return response()->json([
                'success' => true,
                'data' => $activities,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load activities',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Clear dashboard cache
     */
    public function clearCache(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $this->dashboardService->clearDashboardCache($user->id);

            return response()->json([
                'success' => true,
                'message' => 'Dashboard cache cleared successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get role-specific widgets configuration
     */
    public function widgets(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $role = $user->roles->first()->name ?? 'siswa';

            $widgets = $this->dashboardService->getRoleWidgets($role);

            return response()->json([
                'success' => true,
                'data' => $widgets,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load widgets',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
            ], 500);
        }
    }
}
