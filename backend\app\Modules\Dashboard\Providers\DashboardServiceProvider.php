<?php

namespace App\Modules\Dashboard\Providers;

use Illuminate\Support\ServiceProvider;

class DashboardServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register dashboard services
        $this->app->bind(
            \App\Modules\Dashboard\Repositories\DashboardRepositoryInterface::class,
            \App\Modules\Dashboard\Repositories\DashboardRepository::class
        );

        $this->app->bind(
            \App\Modules\Dashboard\Services\DashboardServiceInterface::class,
            \App\Modules\Dashboard\Services\DashboardService::class
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load module routes
        $this->loadRoutesFrom(__DIR__ . '/../routes/web.php');
        $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');

        // Load module views
        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'dashboard');

        // Load module migrations
        $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');

        // Load module config
        $this->mergeConfigFrom(__DIR__ . '/../config/dashboard.php', 'dashboard');

        // Publish config
        $this->publishes([
            __DIR__ . '/../config/dashboard.php' => config_path('dashboard.php'),
        ], 'dashboard-config');

        // Publish assets
        $this->publishes([
            __DIR__ . '/../resources/assets' => public_path('modules/dashboard'),
        ], 'dashboard-assets');
    }
}
