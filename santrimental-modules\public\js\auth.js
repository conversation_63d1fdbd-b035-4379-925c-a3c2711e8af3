// Authentication Module
class AuthManager {
    constructor() {
        this.baseURL = 'http://localhost:8000/api'; // Updated for Laravel backend
        this.token = localStorage.getItem('authToken');
        this.user = JSON.parse(localStorage.getItem('user') || 'null');
    }

    // Show loading overlay
    showLoading() {
        document.getElementById('loading-overlay').classList.remove('hidden');
    }

    // Hide loading overlay
    hideLoading() {
        document.getElementById('loading-overlay').classList.add('hidden');
    }

    // Show toast notification
    showToast(title, message, type = 'info') {
        const toast = document.getElementById('toast');
        const toastIcon = document.getElementById('toast-icon');
        const toastTitle = document.getElementById('toast-title');
        const toastMessage = document.getElementById('toast-message');

        // Set icon based on type
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        toastIcon.textContent = icons[type] || icons.info;
        toastTitle.textContent = title;
        toastMessage.textContent = message;

        toast.classList.remove('hidden');
        
        // Auto hide after 5 seconds
        setTimeout(() => {
            toast.classList.add('hidden');
        }, 5000);
    }

    // Validate email format
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Validate password strength
    validatePassword(password) {
        return password.length >= 8;
    }

    // Login with email and password
    async login(email, password) {
        try {
            this.showLoading();

            // Validate inputs
            if (!this.validateEmail(email)) {
                throw new Error('Format email tidak valid');
            }

            if (!password) {
                throw new Error('Password tidak boleh kosong');
            }

            // Real API call
            const response = await this.makeAPICall('/login', {
                method: 'POST',
                body: JSON.stringify({
                    email,
                    password
                })
            });

            if (response.token) {
                this.token = response.token;
                this.user = response.user;

                // Store in localStorage
                localStorage.setItem('authToken', this.token);
                localStorage.setItem('user', JSON.stringify(this.user));

                // Store role information
                if (response.role) {
                    localStorage.setItem('user_role', JSON.stringify(response.role));
                }

                this.showToast('Berhasil!', 'Login berhasil. Mengalihkan ke dashboard...', 'success');

                // Redirect to dashboard after short delay
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1500);
            } else {
                throw new Error(response.message || 'Login gagal');
            }

        } catch (error) {
            this.showToast('Error', error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Register new user
    async register(userData) {
        try {
            this.showLoading();

            // Validate inputs
            if (!userData.firstName || !userData.lastName) {
                throw new Error('Nama lengkap harus diisi');
            }

            if (!this.validateEmail(userData.email)) {
                throw new Error('Format email tidak valid');
            }

            if (!this.validatePassword(userData.password)) {
                throw new Error('Password minimal 8 karakter');
            }

            if (userData.password !== userData.confirmPassword) {
                throw new Error('Konfirmasi password tidak cocok');
            }

            // Real API call
            const response = await this.makeAPICall('/register', {
                method: 'POST',
                body: JSON.stringify({
                    first_name: userData.firstName,
                    last_name: userData.lastName,
                    email: userData.email,
                    password: userData.password,
                    password_confirmation: userData.confirmPassword
                })
            });

            if (response.user) {
                this.showToast('Berhasil!', 'Registrasi berhasil. Silakan login.', 'success');

                // Switch to login tab
                document.getElementById('login-tab').click();

                // Pre-fill email
                document.getElementById('login-email').value = userData.email;
            } else {
                throw new Error(response.message || 'Registrasi gagal');
            }

        } catch (error) {
            this.showToast('Error', error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Google OAuth login
    async loginWithGoogle() {
        try {
            this.showLoading();
            
            // Simulate Google OAuth (replace with actual Google OAuth)
            const response = await this.simulateAPICall('/auth/google', {
                provider: 'google'
            });

            if (response.success) {
                this.token = response.token;
                this.user = response.user;

                localStorage.setItem('authToken', this.token);
                localStorage.setItem('user', JSON.stringify(this.user));

                // Store role information
                if (response.role) {
                    localStorage.setItem('user_role', JSON.stringify(response.role));
                }

                this.showToast('Berhasil!', 'Login dengan Google berhasil!', 'success');
                
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1500);
            } else {
                throw new Error(response.message || 'Login Google gagal');
            }

        } catch (error) {
            this.showToast('Error', error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    // QR Code login
    async loginWithQR(qrData) {
        try {
            this.showLoading();

            const response = await this.simulateAPICall('/auth/qr-login', {
                qrData
            });

            if (response.success) {
                this.token = response.token;
                this.user = response.user;

                localStorage.setItem('authToken', this.token);
                localStorage.setItem('user', JSON.stringify(this.user));

                // Store role information
                if (response.role) {
                    localStorage.setItem('user_role', JSON.stringify(response.role));
                }

                this.showToast('Berhasil!', 'Login dengan QR Code berhasil!', 'success');
                
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1500);
            } else {
                throw new Error(response.message || 'Login QR Code gagal');
            }

        } catch (error) {
            this.showToast('Error', error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Logout
    logout() {
        this.token = null;
        this.user = null;
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
        localStorage.removeItem('user_role');
        window.location.href = '/';
    }

    // Check if user is authenticated
    isAuthenticated() {
        // Temporarily return true for testing dashboard
        return true;
        // return !!this.token;
    }

    // Get current user
    getCurrentUser() {
        // Return mock user data for testing
        return this.user || {
            id: 1,
            name: 'Demo User',
            email: '<EMAIL>',
            role: 'siswa',
            avatar: null
        };
    }

    // Simulate API call (replace with actual fetch calls)
    async simulateAPICall(endpoint, data) {
        return new Promise((resolve) => {
            setTimeout(() => {
                // Simulate different responses based on endpoint
                switch (endpoint) {
                    case '/auth/login':
                        if (data.email === '<EMAIL>' && data.password === 'demo123') {
                            resolve({
                                success: true,
                                token: 'demo-jwt-token-' + Date.now(),
                                user: {
                                    id: 1,
                                    email: data.email,
                                    firstName: 'Demo',
                                    lastName: 'User',
                                    avatar: null
                                }
                            });
                        } else {
                            resolve({
                                success: false,
                                message: 'Email atau password salah'
                            });
                        }
                        break;
                    
                    case '/auth/register':
                        resolve({
                            success: true,
                            message: 'Registrasi berhasil'
                        });
                        break;
                    
                    case '/auth/google':
                        resolve({
                            success: true,
                            token: 'google-jwt-token-' + Date.now(),
                            user: {
                                id: 2,
                                email: '<EMAIL>',
                                firstName: 'Google',
                                lastName: 'User',
                                avatar: 'https://via.placeholder.com/40'
                            }
                        });
                        break;
                    
                    case '/auth/qr-login':
                        resolve({
                            success: true,
                            token: 'qr-jwt-token-' + Date.now(),
                            user: {
                                id: 3,
                                email: '<EMAIL>',
                                firstName: 'QR',
                                lastName: 'User',
                                avatar: null
                            }
                        });
                        break;
                    
                    default:
                        resolve({
                            success: false,
                            message: 'Endpoint tidak ditemukan'
                        });
                }
            }, 1000); // Simulate network delay
        });
    }



    // Make API calls for authentication
    async makeAPICall(endpoint, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };

        const mergedOptions = { ...defaultOptions, ...options };

        // Add CSRF token for POST requests
        if (mergedOptions.method === 'POST') {
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                             document.querySelector('input[name="_token"]')?.value;
            if (csrfToken) {
                mergedOptions.headers['X-CSRF-TOKEN'] = csrfToken;
            }
        }

        try {
            const response = await fetch(`${this.baseURL}${endpoint}`, mergedOptions);
            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.message || `HTTP ${response.status}`);
            }

            return result;
        } catch (error) {
            console.error('API call failed:', error);
            throw error;
        }
    }

    // Make authenticated API calls
    async apiCall(endpoint, method = 'GET', data = null) {
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            }
        };

        if (data) {
            options.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(`${this.baseURL}${endpoint}`, options);
            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.message || 'API call failed');
            }

            return result;
        } catch (error) {
            // If token is invalid, logout
            if (error.message.includes('401') || error.message.includes('Unauthorized')) {
                this.logout();
            }
            throw error;
        }
    }
}

// Initialize auth manager
const auth = new AuthManager();

// DOM event listeners
document.addEventListener('DOMContentLoaded', () => {
    // Login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            await auth.login(email, password);
        });
    }

    // Register form
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const userData = {
                firstName: document.getElementById('register-firstname').value,
                lastName: document.getElementById('register-lastname').value,
                email: document.getElementById('register-email').value,
                password: document.getElementById('register-password').value,
                confirmPassword: document.getElementById('register-confirm-password').value
            };
            await auth.register(userData);
        });
    }

    // Google login
    const googleLogin = document.getElementById('google-login');
    if (googleLogin) {
        googleLogin.addEventListener('click', async () => {
            await auth.loginWithGoogle();
        });
    }

    // Demo credentials info
    const loginEmail = document.getElementById('login-email');
    if (loginEmail) {
        loginEmail.addEventListener('focus', () => {
            auth.showToast('Demo', 'Gunakan: <EMAIL> / demo123', 'info');
        });
    }
});

// Export for use in other modules
window.auth = auth;
