<?php

use Illuminate\Support\Facades\Route;
use App\Modules\Assessment\Http\Controllers\Api\AssessmentController;

/*
|--------------------------------------------------------------------------
| Assessment API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the Assessment module.
| These routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group.
|
*/

Route::prefix('assessments')->group(function () {
    // Public routes (with auth middleware applied in controller)
    Route::get('/', [AssessmentController::class, 'index']);
    Route::get('/available', [AssessmentController::class, 'available']);
    Route::get('/categories', [AssessmentController::class, 'categories']);
    Route::get('/statistics', [AssessmentController::class, 'statistics']);
    Route::get('/history', [AssessmentController::class, 'history']);
    Route::get('/{code}', [AssessmentController::class, 'show']);
    
    // Assessment actions
    Route::post('/start', [AssessmentController::class, 'start']);
    Route::post('/submit', [AssessmentController::class, 'submit']);
    Route::post('/save-progress', [AssessmentController::class, 'saveProgress']);
    
    // Admin routes
    Route::get('/admin/statistics', [AssessmentController::class, 'overallStatistics']);
});
