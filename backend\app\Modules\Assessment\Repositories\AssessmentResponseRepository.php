<?php

namespace App\Modules\Assessment\Repositories;

use App\Core\Repositories\BaseRepository;
use App\Modules\Assessment\Models\AssessmentResponse;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class AssessmentResponseRepository extends BaseRepository
{
    public function __construct(AssessmentResponse $model)
    {
        parent::__construct($model);
    }

    /**
     * Get user responses
     */
    public function getUserResponses(int $userId): Collection
    {
        return $this->model->where('user_id', $userId)
                          ->with('form')
                          ->latest()
                          ->get();
    }

    /**
     * Get user's latest response for a form
     */
    public function getUserLatestResponse(int $userId, string $formId): ?AssessmentResponse
    {
        return $this->model->where('user_id', $userId)
                          ->where('form_id', $formId)
                          ->latest()
                          ->first();
    }

    /**
     * Get user's completed responses
     */
    public function getUserCompletedResponses(int $userId): Collection
    {
        return $this->model->where('user_id', $userId)
                          ->where('status', AssessmentResponse::STATUS_COMPLETED)
                          ->with('form')
                          ->latest()
                          ->get();
    }

    /**
     * Get user's in-progress responses
     */
    public function getUserInProgressResponses(int $userId): Collection
    {
        return $this->model->where('user_id', $userId)
                          ->whereIn('status', [
                              AssessmentResponse::STATUS_STARTED,
                              AssessmentResponse::STATUS_IN_PROGRESS
                          ])
                          ->with('form')
                          ->latest()
                          ->get();
    }

    /**
     * Get responses by form
     */
    public function getResponsesByForm(string $formId): Collection
    {
        return $this->model->where('form_id', $formId)
                          ->with('user')
                          ->latest()
                          ->get();
    }

    /**
     * Get completed responses by form
     */
    public function getCompletedResponsesByForm(string $formId): Collection
    {
        return $this->model->where('form_id', $formId)
                          ->where('status', AssessmentResponse::STATUS_COMPLETED)
                          ->with('user')
                          ->latest()
                          ->get();
    }

    /**
     * Get response statistics for user
     */
    public function getUserStatistics(int $userId): array
    {
        $total = $this->model->where('user_id', $userId)->count();
        $completed = $this->model->where('user_id', $userId)
                                ->where('status', AssessmentResponse::STATUS_COMPLETED)
                                ->count();
        $inProgress = $this->model->where('user_id', $userId)
                                 ->whereIn('status', [
                                     AssessmentResponse::STATUS_STARTED,
                                     AssessmentResponse::STATUS_IN_PROGRESS
                                 ])
                                 ->count();

        $latestResponse = $this->model->where('user_id', $userId)
                                     ->where('status', AssessmentResponse::STATUS_COMPLETED)
                                     ->latest()
                                     ->first();

        return [
            'total_responses' => $total,
            'completed_responses' => $completed,
            'in_progress_responses' => $inProgress,
            'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
            'latest_response' => $latestResponse,
        ];
    }

    /**
     * Get response statistics for form
     */
    public function getFormStatistics(string $formId): array
    {
        $total = $this->model->where('form_id', $formId)->count();
        $completed = $this->model->where('form_id', $formId)
                                ->where('status', AssessmentResponse::STATUS_COMPLETED)
                                ->count();

        $averageScore = $this->model->where('form_id', $formId)
                                   ->where('status', AssessmentResponse::STATUS_COMPLETED)
                                   ->whereNotNull('score_data')
                                   ->get()
                                   ->avg(function ($response) {
                                       return $response->score_data['total_score'] ?? 0;
                                   });

        $averageCompletionTime = $this->model->where('form_id', $formId)
                                            ->where('status', AssessmentResponse::STATUS_COMPLETED)
                                            ->whereNotNull('completion_time')
                                            ->avg('completion_time');

        return [
            'total_responses' => $total,
            'completed_responses' => $completed,
            'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
            'average_score' => round($averageScore ?? 0, 2),
            'average_completion_time' => round($averageCompletionTime ?? 0),
        ];
    }

    /**
     * Get responses with pagination and filters
     */
    public function getWithFilters(array $filters): LengthAwarePaginator
    {
        $query = $this->model->newQuery()->with(['user', 'form']);

        // Apply user filter
        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        // Apply form filter
        if (!empty($filters['form_id'])) {
            $query->where('form_id', $filters['form_id']);
        }

        // Apply status filter
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Apply date range filter
        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        // Apply search
        if (!empty($filters['search'])) {
            $query->whereHas('user', function ($q) use ($filters) {
                $q->where('first_name', 'like', "%{$filters['search']}%")
                  ->orWhere('last_name', 'like', "%{$filters['search']}%")
                  ->orWhere('email', 'like', "%{$filters['search']}%");
            })->orWhereHas('form', function ($q) use ($filters) {
                $q->where('name', 'like', "%{$filters['search']}%")
                  ->orWhere('code', 'like', "%{$filters['search']}%");
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortOrder = $filters['sort_order'] ?? 'desc';
        $query->orderBy($sortBy, $sortOrder);

        // Apply pagination
        $perPage = min($filters['per_page'] ?? 15, 100);
        
        return $query->paginate($perPage);
    }

    /**
     * Get recent responses
     */
    public function getRecentResponses(int $limit = 10): Collection
    {
        return $this->model->with(['user', 'form'])
                          ->latest()
                          ->limit($limit)
                          ->get();
    }

    /**
     * Get responses by date range
     */
    public function getByDateRange(string $from, string $to): Collection
    {
        return $this->model->whereBetween('created_at', [$from, $to])
                          ->with(['user', 'form'])
                          ->get();
    }

    /**
     * Get completion trends
     */
    public function getCompletionTrends(int $days = 30): array
    {
        $responses = $this->model->where('created_at', '>=', now()->subDays($days))
                                ->where('status', AssessmentResponse::STATUS_COMPLETED)
                                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                                ->groupBy('date')
                                ->orderBy('date')
                                ->get();

        return $responses->pluck('count', 'date')->toArray();
    }

    /**
     * Apply search filters to query
     */
    protected function applySearch(Builder $query, string $search): Builder
    {
        return $query->whereHas('user', function ($q) use ($search) {
            $q->where('first_name', 'like', "%{$search}%")
              ->orWhere('last_name', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%");
        })->orWhereHas('form', function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('code', 'like', "%{$search}%");
        });
    }
}
