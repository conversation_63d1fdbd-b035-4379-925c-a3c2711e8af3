@echo off
echo ========================================
echo SantriMental Fresh Installation Tester
echo ========================================
echo.

set "ORIGINAL_DIR=%cd%"
set "TEST_DIR=C:\laragon\www\santrimental-fresh-test"
set "MODULES_SOURCE=%cd%\santrimental-modules"

echo This script will:
echo 1. Create fresh Laravel installation in: %TEST_DIR%
echo 2. Install SantriMental modules
echo 3. Test complete functionality
echo 4. Keep your original installation intact
echo.
echo Your original installation will remain at: %ORIGINAL_DIR%
echo.
set /p "CONTINUE=Continue? (y/n): "
if /i not "%CONTINUE%"=="y" (
    echo Installation cancelled.
    pause
    exit /b 0
)

echo.
echo ========================================
echo Step 1: Creating Fresh Laravel Project
echo ========================================
echo.

REM Check if test directory exists
if exist "%TEST_DIR%" (
    echo Test directory already exists. Removing...
    rmdir /s /q "%TEST_DIR%"
)

REM Create fresh Laravel project
echo Creating fresh Laravel project...
cd C:\laragon\www\
composer create-project laravel/laravel santrimental-fresh-test --no-interaction

if %errorLevel% neq 0 (
    echo ERROR: Failed to create Laravel project!
    pause
    exit /b 1
)

echo Fresh Laravel project created successfully!

echo.
echo ========================================
echo Step 2: Installing SantriMental Modules
echo ========================================
echo.

cd "%TEST_DIR%"

REM Copy modules to fresh installation
echo Copying SantriMental modules...
xcopy "%MODULES_SOURCE%" "%TEST_DIR%\santrimental-modules" /E /I /H /Y

if %errorLevel% neq 0 (
    echo ERROR: Failed to copy modules!
    pause
    exit /b 1
)

echo Modules copied successfully!

echo.
echo Installing modules to Laravel...

REM Install views
echo Installing views...
copy "santrimental-modules\resources\views\*.blade.php" "resources\views\" /Y

REM Install assets
echo Installing JavaScript and CSS...
if not exist "public\js" mkdir "public\js"
if not exist "public\css" mkdir "public\css"
copy "santrimental-modules\public\js\*.js" "public\js\" /Y
copy "santrimental-modules\public\css\*.css" "public\css\" /Y

REM Install controllers
echo Installing controllers...
if not exist "app\Http\Controllers\Api" mkdir "app\Http\Controllers\Api"
copy "santrimental-modules\app\Http\Controllers\Api\*.php" "app\Http\Controllers\Api\" /Y

REM Install models
echo Installing models...
copy "santrimental-modules\app\Models\*.php" "app\Models\" /Y

REM Install migrations
echo Installing migrations...
copy "santrimental-modules\database\migrations\*.php" "database\migrations\" /Y

REM Install seeders
echo Installing seeders...
copy "santrimental-modules\database\seeders\*.php" "database\seeders\" /Y

REM Install routes
echo Installing routes...
copy "santrimental-modules\routes\web.php" "routes\web.php" /Y
copy "santrimental-modules\routes\api.php" "routes\api.php" /Y

REM Install middleware
echo Installing middleware...
copy "santrimental-modules\app\Http\Middleware\RoleMiddleware.php" "app\Http\Middleware\" /Y

echo All modules installed successfully!

echo.
echo ========================================
echo Step 3: Configuring Environment
echo ========================================
echo.

REM Update .env file
echo Updating .env configuration...
(
echo APP_NAME=SantriMental
echo APP_ENV=local
echo APP_KEY=
echo APP_DEBUG=true
echo APP_URL=http://localhost:8001
echo.
echo LOG_CHANNEL=stack
echo LOG_DEPRECATIONS_CHANNEL=null
echo LOG_LEVEL=debug
echo.
echo DB_CONNECTION=mysql
echo DB_HOST=127.0.0.1
echo DB_PORT=3306
echo DB_DATABASE=santrimental_fresh_test
echo DB_USERNAME=root
echo DB_PASSWORD=
echo.
echo BROADCAST_DRIVER=log
echo CACHE_DRIVER=file
echo FILESYSTEM_DISK=local
echo QUEUE_CONNECTION=sync
echo SESSION_DRIVER=file
echo SESSION_LIFETIME=120
echo.
echo MEMCACHED_HOST=127.0.0.1
echo.
echo REDIS_HOST=127.0.0.1
echo REDIS_PASSWORD=null
echo REDIS_PORT=6379
echo.
echo MAIL_MAILER=smtp
echo MAIL_HOST=mailpit
echo MAIL_PORT=1025
echo MAIL_USERNAME=null
echo MAIL_PASSWORD=null
echo MAIL_ENCRYPTION=null
echo MAIL_FROM_ADDRESS="<EMAIL>"
echo MAIL_FROM_NAME="${APP_NAME}"
echo.
echo # SantriMental Specific
echo FRONTEND_URL=http://localhost:8001
echo SESSION_DOMAIN=localhost
echo SANCTUM_STATEFUL_DOMAINS=localhost
) > .env

echo Environment configured successfully!

echo.
echo ========================================
echo Step 4: Setting Up Database
echo ========================================
echo.

echo Creating database...
mysql -u root -e "CREATE DATABASE IF NOT EXISTS santrimental_fresh_test;"

if %errorLevel% neq 0 (
    echo WARNING: Could not create database automatically.
    echo Please create database manually: santrimental_fresh_test
    pause
)

echo Installing Composer dependencies...
composer install --no-interaction

echo Generating application key...
php artisan key:generate --no-interaction

echo Running migrations...
php artisan migrate --no-interaction

if %errorLevel% neq 0 (
    echo ERROR: Migration failed!
    echo Please check database configuration
    pause
    exit /b 1
)

echo Seeding sample data...
php artisan db:seed --no-interaction

if %errorLevel% neq 0 (
    echo ERROR: Seeding failed!
    pause
    exit /b 1
)

echo Database setup completed successfully!

echo.
echo ========================================
echo Step 5: Testing Installation
echo ========================================
echo.

echo Starting development server on port 8001...
echo.
echo Testing URLs will be:
echo - Home: http://localhost:8001
echo - Student Dashboard: http://localhost:8001/dashboard
echo - Admin Dashboard: http://localhost:8001/admin/dashboard
echo - Teacher Dashboard: http://localhost:8001/guru/dashboard
echo - Parent Dashboard: http://localhost:8001/orangtua/dashboard
echo.
echo Default login credentials:
echo - Admin: <EMAIL> / password
echo - Teacher: <EMAIL> / password
echo - Student: <EMAIL> / password
echo - Parent: <EMAIL> / password
echo.

echo ========================================
echo FRESH INSTALLATION TEST COMPLETE!
echo ========================================
echo.
echo Fresh installation location: %TEST_DIR%
echo Original installation: %ORIGINAL_DIR%
echo.
echo To start testing:
echo 1. cd %TEST_DIR%
echo 2. php artisan serve --port=8001
echo 3. Open browser to http://localhost:8001
echo.
echo To compare with original:
echo 1. cd %ORIGINAL_DIR%\backend
echo 2. php artisan serve --port=8000
echo 3. Open browser to http://localhost:8000
echo.
echo Both installations can run simultaneously!
echo.

set /p "START_SERVER=Start fresh installation server now? (y/n): "
if /i "%START_SERVER%"=="y" (
    echo Starting server...
    php artisan serve --port=8001
) else (
    echo.
    echo To start server later, run:
    echo cd %TEST_DIR%
    echo php artisan serve --port=8001
)

echo.
pause
