<?php

use App\Http\Controllers\Api\AuthController;
// use App\Http\Controllers\Api\AssessmentController; // Legacy controller - using modular controller instead
use App\Http\Controllers\Api\FormTemplateController;
use App\Http\Controllers\Api\RoleController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Public routes - MOVED TO AUTH MODULE
// These routes are now handled by App\Modules\Auth\Http\Controllers\Api\AuthController
// Route::post('/register', [AuthController::class, 'register']); // DISABLED - Use /api/auth/register
// Route::post('/login', [AuthController::class, 'login']); // DISABLED - Use /api/auth/login
// Route::post('/auth/google', [AuthController::class, 'googleAuth']); // DISABLED - Use /api/auth/google
Route::post('/auth/qr', [AuthController::class, 'qrLogin']); // Keep QR login for now

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes - MOVED TO AUTH MODULE
    // Route::get('/user', [AuthController::class, 'user']); // DISABLED - Use /api/auth/user
    // Route::post('/logout', [AuthController::class, 'logout']); // DISABLED - Use /api/auth/logout

    // Assessment routes (legacy) - Commented out in favor of modular routes
    // Route::prefix('assessments')->group(function () {
    //     Route::get('/', [AssessmentController::class, 'index']);
    //     Route::post('/', [AssessmentController::class, 'store']);
    //     Route::get('/questions', [AssessmentController::class, 'questions']);
    //     Route::get('/dashboard-stats', [AssessmentController::class, 'dashboardStats']);
    //     Route::get('/monthly-report', [AssessmentController::class, 'monthlyReport']);
    //     Route::get('/{assessment}', [AssessmentController::class, 'show']);
    // });

    // Dynamic Form System routes
    Route::prefix('forms')->group(function () {
        Route::get('/', [FormTemplateController::class, 'index']);
        Route::get('/{code}', [FormTemplateController::class, 'show']);
        // Submit moved to siswa-only section
        Route::get('/{code}/responses', [FormTemplateController::class, 'getUserResponses']);
    });

    // Dashboard stats (new dynamic system)
    Route::get('/dashboard/stats', [FormTemplateController::class, 'getDashboardStats']);

    // Role-based routes
    Route::get('/user/role', [RoleController::class, 'getUserRole']);
    Route::get('/dashboard/role-data', [RoleController::class, 'getDashboardData']);

    // Admin only routes
    Route::middleware(['role:admin'])->group(function () {
        Route::get('/admin/users', function () {
            return response()->json(['message' => 'Admin users endpoint']);
        });
        Route::get('/admin/reports', function () {
            return response()->json(['message' => 'Admin reports endpoint']);
        });
    });

    // Guru only routes
    Route::middleware(['role:guru'])->group(function () {
        Route::get('/guru/students', function () {
            return response()->json(['message' => 'Guru students endpoint']);
        });
        Route::get('/guru/class-reports', function () {
            return response()->json(['message' => 'Guru class reports endpoint']);
        });
    });

    // Orangtua only routes
    Route::middleware(['role:orangtua'])->group(function () {
        Route::get('/orangtua/children', function () {
            return response()->json(['message' => 'Orangtua children endpoint']);
        });
    });

    // Siswa only routes
    Route::middleware(['role:siswa'])->group(function () {
        Route::post('/forms/{code}/submit', [FormTemplateController::class, 'submitResponse']);
    });
});

// Fallback for undefined routes
Route::fallback(function () {
    return response()->json([
        'message' => 'Route not found.'
    ], 404);
});
