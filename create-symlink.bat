@echo off
echo ========================================
echo   SantriMental - Create Symlink
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator... OK
) else (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo.
echo Creating symlink for easier access...

REM Remove existing symlink if exists
if exist "C:\laragon\www\santrimental-public" (
    rmdir "C:\laragon\www\santrimental-public"
    echo Removed existing symlink
)

REM Create new symlink
mklink /D "C:\laragon\www\santrimental-public" "C:\laragon\www\santrimental\backend\public"

if %errorLevel% == 0 (
    echo.
    echo ========================================
    echo   Symlink Created Successfully!
    echo ========================================
    echo.
    echo You can now access SantriMental at:
    echo   http://localhost/santrimental-public
    echo.
) else (
    echo.
    echo ERROR: Failed to create symlink
    echo Make sure you're running as Administrator
    echo.
)

pause
