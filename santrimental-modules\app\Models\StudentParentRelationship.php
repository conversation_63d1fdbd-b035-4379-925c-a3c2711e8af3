<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudentParentRelationship extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'parent_id',
        'relationship_type',
        'is_primary',
        'can_view_results'
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'can_view_results' => 'boolean'
    ];

    public function student()
    {
        return $this->belongsTo(User::class, 'student_id');
    }

    public function parent()
    {
        return $this->belongsTo(User::class, 'parent_id');
    }

    // Scope for primary contacts
    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    // Scope for those who can view results
    public function scopeCanViewResults($query)
    {
        return $query->where('can_view_results', true);
    }
}
