<?php

namespace App\Core\Services;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

abstract class BaseService
{
    /**
     * Execute a database transaction with error handling
     */
    protected function executeTransaction(callable $callback, string $operation = 'operation')
    {
        try {
            DB::beginTransaction();
            
            $result = $callback();
            
            DB::commit();
            
            Log::info("Successfully executed {$operation}");
            
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error("Failed to execute {$operation}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    /**
     * Log model activity
     */
    protected function logActivity(Model $model, string $action, array $data = [])
    {
        Log::info("Model activity: {$action}", [
            'model' => get_class($model),
            'id' => $model->getKey(),
            'data' => $data
        ]);
    }

    /**
     * Validate required fields
     */
    protected function validateRequired(array $data, array $required): void
    {
        $missing = array_diff($required, array_keys($data));
        
        if (!empty($missing)) {
            throw new \InvalidArgumentException(
                'Missing required fields: ' . implode(', ', $missing)
            );
        }
    }

    /**
     * Filter array by allowed keys
     */
    protected function filterAllowedFields(array $data, array $allowed): array
    {
        return array_intersect_key($data, array_flip($allowed));
    }

    /**
     * Handle pagination parameters
     */
    protected function getPaginationParams(array $params): array
    {
        return [
            'page' => (int) ($params['page'] ?? 1),
            'per_page' => min((int) ($params['per_page'] ?? 15), 100),
            'search' => $params['search'] ?? null,
            'sort_by' => $params['sort_by'] ?? 'created_at',
            'sort_order' => in_array($params['sort_order'] ?? 'desc', ['asc', 'desc']) 
                ? $params['sort_order'] 
                : 'desc'
        ];
    }
}
