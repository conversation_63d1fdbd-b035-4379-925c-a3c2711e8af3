@echo off
echo ========================================
echo SantriMental - Fresh Start Cleanup
echo ========================================

set PROJECT_DIR=C:\laragon\www\santrimental

echo WARNING: This will delete the entire project directory!
echo Project directory: %PROJECT_DIR%
echo.
set /p confirm="Are you sure you want to continue? (y/N): "

if /i "%confirm%" NEQ "y" (
    echo Operation cancelled.
    pause
    exit /b
)

echo.
echo Starting cleanup...

REM Stop any running processes
echo - Stopping any running Laravel processes...
taskkill /f /im php.exe 2>nul
taskkill /f /im node.exe 2>nul

REM Remove project directory
if exist "%PROJECT_DIR%" (
    echo - Removing project directory...
    rmdir /s /q "%PROJECT_DIR%"
    echo - Project directory removed
) else (
    echo - Project directory not found
)

REM Clean Laragon virtual hosts
echo - Cleaning Laragon virtual hosts...
if exist "C:\laragon\etc\apache2\sites-enabled\santrimental.me.conf" (
    del "C:\laragon\etc\apache2\sites-enabled\santrimental.me.conf"
    echo - Removed virtual host configuration
)

REM Clean hosts file (optional)
echo.
set /p clean_hosts="Remove santrimental.me from hosts file? (y/N): "
if /i "%clean_hosts%" == "y" (
    echo - Cleaning hosts file...
    powershell -Command "(Get-Content C:\Windows\System32\drivers\etc\hosts) | Where-Object { $_ -notmatch 'santrimental.me' } | Set-Content C:\Windows\System32\drivers\etc\hosts"
    echo - Hosts file cleaned
)

echo.
echo ========================================
echo Fresh start cleanup completed!
echo ========================================
echo.
echo Next steps:
echo 1. Create new project directory
echo 2. Setup Laravel backend
echo 3. Configure virtual host
echo 4. Setup frontend (if needed)
echo.
pause
