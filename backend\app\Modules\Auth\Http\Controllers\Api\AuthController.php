<?php

namespace App\Modules\Auth\Http\Controllers\Api;

use App\Core\Http\Controllers\BaseApiController;
use App\Modules\Auth\Http\Requests\LoginRequest;
use App\Modules\Auth\Http\Requests\RegisterRequest;
use App\Modules\Auth\Http\Resources\UserResource;
use App\Modules\Auth\Services\AuthService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AuthController extends BaseApiController
{
    public function __construct(
        private AuthService $authService
    ) {}

    /**
     * Register a new user
     */
    public function register(RegisterRequest $request): JsonResponse
    {
        try {
            $result = $this->authService->register($request->validated());
            
            return $this->successResponse([
                'user' => new UserResource($result['user']),
                'token' => $result['token'],
                'expires_at' => $result['expires_at']
            ], 'Registration successful', 201);
            
        } catch (\Exception $e) {
            return $this->errorResponse('Registration failed: ' . $e->getMessage(), 422);
        }
    }

    /**
     * Login user
     */
    public function login(LoginRequest $request): JsonResponse
    {
        try {
            $result = $this->authService->login($request->validated());
            
            if (!$result) {
                return $this->errorResponse('Invalid credentials', 401);
            }
            
            return $this->successResponse([
                'user' => new UserResource($result['user']),
                'token' => $result['token'],
                'expires_at' => $result['expires_at']
            ], 'Login successful');
            
        } catch (\Exception $e) {
            return $this->errorResponse('Login failed: ' . $e->getMessage(), 422);
        }
    }

    /**
     * Logout user
     */
    public function logout(Request $request): JsonResponse
    {
        try {
            $this->authService->logout($request->user());
            
            return $this->successResponse(null, 'Logout successful');
            
        } catch (\Exception $e) {
            return $this->errorResponse('Logout failed: ' . $e->getMessage(), 422);
        }
    }

    /**
     * Get authenticated user
     */
    public function user(Request $request): JsonResponse
    {
        try {
            $user = $this->authService->getAuthenticatedUser($request->user());
            
            return $this->successResponse(
                new UserResource($user),
                'User retrieved successfully'
            );
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve user: ' . $e->getMessage(), 422);
        }
    }

    /**
     * Refresh token
     */
    public function refresh(Request $request): JsonResponse
    {
        try {
            $result = $this->authService->refreshToken($request->user());
            
            return $this->successResponse([
                'token' => $result['token'],
                'expires_at' => $result['expires_at']
            ], 'Token refreshed successfully');
            
        } catch (\Exception $e) {
            return $this->errorResponse('Token refresh failed: ' . $e->getMessage(), 422);
        }
    }

    /**
     * Google OAuth login
     */
    public function googleAuth(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'access_token' => 'required|string'
            ]);
            
            $result = $this->authService->googleAuth($request->access_token);
            
            return $this->successResponse([
                'user' => new UserResource($result['user']),
                'token' => $result['token'],
                'expires_at' => $result['expires_at']
            ], 'Google authentication successful');
            
        } catch (\Exception $e) {
            return $this->errorResponse('Google authentication failed: ' . $e->getMessage(), 422);
        }
    }

    /**
     * Forgot password
     */
    public function forgotPassword(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'email' => 'required|email|exists:users,email'
            ]);
            
            $this->authService->sendPasswordResetLink($request->email);
            
            return $this->successResponse(null, 'Password reset link sent to your email');
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to send reset link: ' . $e->getMessage(), 422);
        }
    }

    /**
     * Reset password
     */
    public function resetPassword(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'token' => 'required|string',
                'email' => 'required|email',
                'password' => 'required|string|min:8|confirmed'
            ]);
            
            $result = $this->authService->resetPassword($request->validated());
            
            if (!$result) {
                return $this->errorResponse('Invalid or expired reset token', 422);
            }
            
            return $this->successResponse(null, 'Password reset successfully');
            
        } catch (\Exception $e) {
            return $this->errorResponse('Password reset failed: ' . $e->getMessage(), 422);
        }
    }

    /**
     * Change password
     */
    public function changePassword(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'current_password' => 'required|string',
                'password' => 'required|string|min:8|confirmed'
            ]);
            
            $result = $this->authService->changePassword(
                $request->user(),
                $request->current_password,
                $request->password
            );
            
            if (!$result) {
                return $this->errorResponse('Current password is incorrect', 422);
            }
            
            return $this->successResponse(null, 'Password changed successfully');
            
        } catch (\Exception $e) {
            return $this->errorResponse('Password change failed: ' . $e->getMessage(), 422);
        }
    }

    /**
     * Verify email
     */
    public function verifyEmail(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'token' => 'required|string'
            ]);
            
            $result = $this->authService->verifyEmail($request->token);
            
            if (!$result) {
                return $this->errorResponse('Invalid or expired verification token', 422);
            }
            
            return $this->successResponse(null, 'Email verified successfully');
            
        } catch (\Exception $e) {
            return $this->errorResponse('Email verification failed: ' . $e->getMessage(), 422);
        }
    }

    /**
     * Resend email verification
     */
    public function resendVerification(Request $request): JsonResponse
    {
        try {
            $this->authService->resendEmailVerification($request->user());
            
            return $this->successResponse(null, 'Verification email sent');
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to send verification email: ' . $e->getMessage(), 422);
        }
    }
}
