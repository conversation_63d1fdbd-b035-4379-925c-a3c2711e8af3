# Enhanced Auth API Test Script
$baseUrl = "http://localhost:8000/api"
$headers = @{
    "Accept" = "application/json"
    "Content-Type" = "application/json"
}

Write-Host "=== Testing Enhanced Auth API ===" -ForegroundColor Green

# Test 1: Register new user
Write-Host "`n1. Testing POST /api/auth/register" -ForegroundColor Yellow
try {
    $registerData = @{
        first_name = "<PERSON>"
        last_name = "<PERSON>e"
        email = "<EMAIL>"
        password = "Password123!"
        password_confirmation = "Password123!"
        phone = "081234567890"
        student_id = "STD002"
        class = "10A"
        grade = 10
        role = "siswa"
        terms_accepted = $true
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "$baseUrl/auth/register" -Method POST -Headers $headers -Body $registerData
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "User registered: $($data.data.user.full_name)" -ForegroundColor Green
    Write-Host "Role: $($data.data.user.role.display_name)" -ForegroundColor Green
    $token = $data.data.token
    Write-Host "Token received: $($token.Substring(0, 20))..." -ForegroundColor Green
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Login with email
Write-Host "`n2. Testing POST /api/auth/login (with email)" -ForegroundColor Yellow
try {
    $loginData = @{
        login = "<EMAIL>"
        password = "Password123!"
        remember = $false
        device_name = "Test Device"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "$baseUrl/auth/login" -Method POST -Headers $headers -Body $loginData
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "Login successful: $($data.data.user.full_name)" -ForegroundColor Green
    $loginToken = $data.data.token
    Write-Host "New token: $($loginToken.Substring(0, 20))..." -ForegroundColor Green
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Login with student ID
Write-Host "`n3. Testing POST /api/auth/login (with student ID)" -ForegroundColor Yellow
try {
    $loginData = @{
        login = "STD002"
        password = "Password123!"
        remember = $true
        device_name = "Mobile App"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "$baseUrl/auth/login" -Method POST -Headers $headers -Body $loginData
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "Login with Student ID successful: $($data.data.user.student_id)" -ForegroundColor Green
    $studentToken = $data.data.token
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Get authenticated user
Write-Host "`n4. Testing GET /api/auth/user" -ForegroundColor Yellow
try {
    $authHeaders = $headers.Clone()
    $authHeaders["Authorization"] = "Bearer $studentToken"
    
    $response = Invoke-WebRequest -Uri "$baseUrl/auth/user" -Method GET -Headers $authHeaders
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "User profile: $($data.data.full_name)" -ForegroundColor Green
    Write-Host "Email verified: $($data.data.email_verified)" -ForegroundColor Green
    Write-Host "Profile completion: $($data.data.profile_completion_percentage)%" -ForegroundColor Green
    Write-Host "Account status: $($data.data.account_status)" -ForegroundColor Green
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Refresh token
Write-Host "`n5. Testing POST /api/auth/refresh" -ForegroundColor Yellow
try {
    $authHeaders = $headers.Clone()
    $authHeaders["Authorization"] = "Bearer $studentToken"
    
    $response = Invoke-WebRequest -Uri "$baseUrl/auth/refresh" -Method POST -Headers $authHeaders
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "Token refreshed successfully" -ForegroundColor Green
    $refreshedToken = $data.data.token
    Write-Host "New token: $($refreshedToken.Substring(0, 20))..." -ForegroundColor Green
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Change password
Write-Host "`n6. Testing POST /api/auth/change-password" -ForegroundColor Yellow
try {
    $authHeaders = $headers.Clone()
    $authHeaders["Authorization"] = "Bearer $refreshedToken"
    
    $changePasswordData = @{
        current_password = "Password123!"
        password = "NewPassword123!"
        password_confirmation = "NewPassword123!"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "$baseUrl/auth/change-password" -Method POST -Headers $authHeaders -Body $changePasswordData
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "Password changed successfully" -ForegroundColor Green
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: Login with new password
Write-Host "`n7. Testing login with new password" -ForegroundColor Yellow
try {
    $loginData = @{
        login = "<EMAIL>"
        password = "NewPassword123!"
        device_name = "Test Device"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "$baseUrl/auth/login" -Method POST -Headers $headers -Body $loginData
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Login with new password successful" -ForegroundColor Green
    $finalToken = ($response.Content | ConvertFrom-Json).data.token
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 8: Logout
Write-Host "`n8. Testing POST /api/auth/logout" -ForegroundColor Yellow
try {
    $authHeaders = $headers.Clone()
    $authHeaders["Authorization"] = "Bearer $finalToken"
    
    $response = Invoke-WebRequest -Uri "$baseUrl/auth/logout" -Method POST -Headers $authHeaders
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Logout successful" -ForegroundColor Green
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 9: Try to access protected route after logout
Write-Host "`n9. Testing access after logout (should fail)" -ForegroundColor Yellow
try {
    $authHeaders = $headers.Clone()
    $authHeaders["Authorization"] = "Bearer $finalToken"
    
    $response = Invoke-WebRequest -Uri "$baseUrl/auth/user" -Method GET -Headers $authHeaders
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Red
    Write-Host "ERROR: Should have failed!" -ForegroundColor Red
} catch {
    Write-Host "Expected error: $($_.Exception.Message)" -ForegroundColor Green
    Write-Host "Token properly revoked" -ForegroundColor Green
}

Write-Host "`n=== Enhanced Auth API Test Complete ===" -ForegroundColor Green
