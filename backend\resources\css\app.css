@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Modern Dashboard CSS Framework */
:root {
  /* Color Palette */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  --secondary-50: #fdf4ff;
  --secondary-100: #fae8ff;
  --secondary-200: #f5d0fe;
  --secondary-300: #f0abfc;
  --secondary-400: #e879f9;
  --secondary-500: #d946ef;
  --secondary-600: #c026d3;
  --secondary-700: #a21caf;
  --secondary-800: #86198f;
  --secondary-900: #701a75;

  --success-50: #f0fdf4;
  --success-100: #dcfce7;
  --success-200: #bbf7d0;
  --success-300: #86efac;
  --success-400: #4ade80;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --success-700: #15803d;
  --success-800: #166534;
  --success-900: #14532d;

  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;

  --danger-50: #fef2f2;
  --danger-100: #fee2e2;
  --danger-200: #fecaca;
  --danger-300: #fca5a5;
  --danger-400: #f87171;
  --danger-500: #ef4444;
  --danger-600: #dc2626;
  --danger-700: #b91c1c;
  --danger-800: #991b1b;
  --danger-900: #7f1d1d;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
}

/* Enhanced Dark Theme Variables */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-quaternary: #475569;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --border-color: #475569;
  --border-light: #64748b;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --glass-bg: rgba(15, 23, 42, 0.8);
  --glass-border: rgba(255, 255, 255, 0.1);
  --accent-primary: #0ea5e9;
  --accent-secondary: #d946ef;
  --success-bg: rgba(34, 197, 94, 0.1);
  --warning-bg: rgba(245, 158, 11, 0.1);
  --error-bg: rgba(239, 68, 68, 0.1);
}

/* Enhanced Light Theme Variables */
[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-quaternary: #e2e8f0;
  --text-primary: #0f172a;
  --text-secondary: #334155;
  --text-tertiary: #64748b;
  --border-color: #e2e8f0;
  --border-light: #cbd5e1;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --glass-bg: rgba(255, 255, 255, 0.9);
  --glass-border: rgba(0, 0, 0, 0.1);
  --accent-primary: #0ea5e9;
  --accent-secondary: #d946ef;
  --success-bg: rgba(34, 197, 94, 0.1);
  --warning-bg: rgba(245, 158, 11, 0.1);
  --error-bg: rgba(239, 68, 68, 0.1);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modern Gradient Backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-500) 0%, var(--primary-500) 100%);
}

.gradient-success {
  background: linear-gradient(135deg, var(--success-500) 0%, var(--primary-500) 100%);
}

.gradient-warning {
  background: linear-gradient(135deg, var(--warning-500) 0%, var(--danger-500) 100%);
}

.gradient-dark {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

.gradient-mesh {
  background:
    radial-gradient(at 40% 20%, var(--primary-500) 0px, transparent 50%),
    radial-gradient(at 80% 0%, var(--secondary-500) 0px, transparent 50%),
    radial-gradient(at 0% 50%, var(--primary-600) 0px, transparent 50%),
    radial-gradient(at 80% 50%, var(--secondary-600) 0px, transparent 50%),
    radial-gradient(at 0% 100%, var(--primary-700) 0px, transparent 50%),
    radial-gradient(at 80% 100%, var(--secondary-700) 0px, transparent 50%),
    radial-gradient(at 0% 0%, var(--primary-800) 0px, transparent 50%);
}

/* Modern Glass Morphism */
.glass-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-card-light {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
}

.glass-card-dark {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Modern Card Styles */
.modern-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
}

.modern-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.modern-card-elevated {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  transition: all var(--transition-normal);
}

.modern-card-elevated:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

/* Modern Buttons */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: 600;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  cursor: pointer;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: 600;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  cursor: pointer;
  box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, var(--secondary-600), var(--secondary-700));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: 500;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.btn-ghost:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--primary-500);
}

/* Modern Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  background-size: 200px 100%;
}

/* Advanced Performance Animations */
@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes flipIn {
  from {
    opacity: 0;
    transform: perspective(400px) rotateX(90deg);
  }
  40% {
    transform: perspective(400px) rotateX(-20deg);
  }
  60% {
    transform: perspective(400px) rotateX(10deg);
  }
  80% {
    transform: perspective(400px) rotateX(-5deg);
  }
  to {
    opacity: 1;
    transform: perspective(400px) rotateX(0deg);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(14, 165, 233, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(14, 165, 233, 0.8), 0 0 30px rgba(14, 165, 233, 0.6);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

/* Animation Classes */
.animate-slide-in-bottom {
  animation: slideInFromBottom 0.6s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.6s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.6s ease-out forwards;
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out forwards;
}

.animate-flip-in {
  animation: flipIn 0.8s ease-out forwards;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-typewriter {
  overflow: hidden;
  white-space: nowrap;
  animation: typewriter 2s steps(40, end);
}

/* Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* GPU Acceleration */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Smooth Scrolling */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Hardware Acceleration for Animations */
.stats-card,
.modern-card,
.chart-container,
.sidebar-item {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimized Hover Effects */
.optimized-hover {
  transition: transform var(--transition-fast) ease-out,
              box-shadow var(--transition-fast) ease-out;
}

.optimized-hover:hover {
  transform: translateY(-2px) translateZ(0);
}

/* Loading Skeleton Improvements */
.skeleton-wave {
  position: relative;
  overflow: hidden;
}

.skeleton-wave::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: shimmer 1.5s infinite;
  transform: translateX(-100%);
}

/* Stagger Animation Delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }
.stagger-7 { animation-delay: 0.7s; }
.stagger-8 { animation-delay: 0.8s; }

/* Micro-interactions */
.micro-bounce:active {
  transform: scale(0.95);
  transition: transform 0.1s ease-out;
}

.micro-glow:hover {
  box-shadow: 0 0 20px rgba(14, 165, 233, 0.3);
  transition: box-shadow var(--transition-fast) ease-out;
}

.micro-lift:hover {
  transform: translateY(-1px);
  transition: transform var(--transition-fast) ease-out;
}

/* Modern Sidebar Styles */
.sidebar-modern {
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  margin: var(--spacing-xs) 0;
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.sidebar-item:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transform: translateX(4px);
}

.sidebar-item.active {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  box-shadow: var(--shadow-md);
}

.sidebar-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--secondary-500);
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.sidebar-icon {
  width: 20px;
  height: 20px;
  margin-right: var(--spacing-sm);
  transition: transform var(--transition-fast);
}

.sidebar-item:hover .sidebar-icon {
  transform: scale(1.1);
}

/* Modern Header Styles */
.header-modern {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Modern Stats Cards */
.stats-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-500);
}

.stats-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
  margin: var(--spacing-sm) 0;
}

.stats-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stats-change {
  font-size: 0.75rem;
  font-weight: 600;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  margin-top: var(--spacing-sm);
}

.stats-change.positive {
  background: var(--success-100);
  color: var(--success-700);
}

.stats-change.negative {
  background: var(--danger-100);
  color: var(--danger-700);
}

/* Modern Chart Container */
.chart-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  position: relative;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.chart-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

/* Modern Table Styles */
.table-modern {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.table-modern th {
  background: var(--bg-tertiary);
  padding: var(--spacing-md);
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table-modern td {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  color: var(--text-secondary);
}

.table-modern tr:hover {
  background: var(--bg-tertiary);
}

/* Modern Form Styles */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-size: 0.875rem;
}

.form-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* Modern Badge Styles */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background: var(--primary-100);
  color: var(--primary-700);
}

.badge-success {
  background: var(--success-100);
  color: var(--success-700);
}

.badge-warning {
  background: var(--warning-100);
  color: var(--warning-700);
}

.badge-danger {
  background: var(--danger-100);
  color: var(--danger-700);
}

/* Modern Loading States */
.skeleton {
  background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-secondary) 50%, var(--bg-tertiary) 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: var(--radius-md);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modern Notification Styles */
.notification {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  z-index: 1000;
  transform: translateX(100%);
  transition: transform var(--transition-normal);
}

.notification.show {
  transform: translateX(0);
}

.notification-success {
  background: rgba(34, 197, 94, 0.9);
  border: 1px solid var(--success-500);
  color: white;
}

.notification-error {
  background: rgba(239, 68, 68, 0.9);
  border: 1px solid var(--danger-500);
  color: white;
}

.notification-warning {
  background: rgba(245, 158, 11, 0.9);
  border: 1px solid var(--warning-500);
  color: white;
}

.notification-info {
  background: rgba(14, 165, 233, 0.9);
  border: 1px solid var(--primary-500);
  color: white;
}

/* Modern Modal Styles */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  transform: scale(0.9);
  transition: transform var(--transition-normal);
}

.modal-overlay.show .modal-content {
  transform: scale(1);
}

/* Responsive Design Utilities */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.grid {
  display: grid;
  gap: var(--spacing-lg);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.space-x-4 > * + * {
  margin-left: var(--spacing-md);
}

.space-y-4 > * + * {
  margin-top: var(--spacing-md);
}

/* Enhanced Responsive Breakpoints */
@media (max-width: 640px) {
  .grid-cols-2 { grid-template-columns: 1fr; }
  .grid-cols-3 { grid-template-columns: 1fr; }
  .grid-cols-4 { grid-template-columns: 1fr; }

  .container {
    padding: 0 var(--spacing-sm);
  }

  .stats-value {
    font-size: 2rem;
  }

  .chart-title {
    font-size: 1rem;
  }

  .chart-subtitle {
    font-size: 0.75rem;
  }

  .sidebar-modern {
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
    width: 280px;
  }

  .sidebar-modern.open {
    transform: translateX(0);
  }

  .header-modern {
    padding: 1rem;
  }

  .stats-card {
    padding: 1rem;
  }

  .modern-card {
    padding: 1rem;
  }

  .chart-container {
    padding: 1rem;
  }

  .notification {
    max-width: calc(100vw - 2rem);
    margin: 0 1rem;
  }

  .modal-content {
    max-width: calc(100vw - 2rem);
    margin: 0 1rem;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .grid-cols-3 { grid-template-columns: repeat(2, 1fr); }
  .grid-cols-4 { grid-template-columns: repeat(2, 1fr); }

  .sidebar-modern {
    width: 260px;
  }

  .stats-value {
    font-size: 2.25rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid-cols-4 { grid-template-columns: repeat(3, 1fr); }
  .grid-cols-5 { grid-template-columns: repeat(3, 1fr); }

  .sidebar-modern {
    width: 264px;
  }
}

@media (min-width: 1025px) {
  .grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
  .sidebar-item {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .btn-primary,
  .btn-secondary,
  .btn-ghost {
    padding: var(--spacing-md) var(--spacing-lg);
    min-height: 44px;
  }

  .stats-card:hover,
  .modern-card:hover,
  .chart-container:hover {
    transform: none;
  }
}

/* Enhanced Theme-Specific Styles */

/* Dark Theme Styles */
[data-theme="dark"] {
  color-scheme: dark;
}

[data-theme="dark"] body {
  background: var(--bg-primary);
  color: var(--text-primary);
}

[data-theme="dark"] .glass-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px var(--shadow-color);
}

[data-theme="dark"] .stats-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 16px var(--shadow-color);
}

[data-theme="dark"] .modern-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 16px var(--shadow-color);
}

[data-theme="dark"] .chart-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

[data-theme="dark"] .table-modern {
  background: var(--bg-secondary);
}

[data-theme="dark"] .form-input {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .sidebar-modern {
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
}

[data-theme="dark"] .header-modern {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

/* Light Theme Styles */
[data-theme="light"] {
  color-scheme: light;
}

[data-theme="light"] body {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: var(--text-primary);
}

[data-theme="light"] .glass-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px var(--shadow-color);
}

[data-theme="light"] .stats-card {
  background: white;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 16px var(--shadow-color);
}

[data-theme="light"] .modern-card {
  background: white;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 16px var(--shadow-color);
}

[data-theme="light"] .chart-container {
  background: white;
  border: 1px solid var(--border-color);
}

[data-theme="light"] .table-modern {
  background: white;
}

[data-theme="light"] .form-input {
  background: white;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

[data-theme="light"] .sidebar-modern {
  background: white;
  border-right: 1px solid var(--border-color);
  box-shadow: 4px 0 16px var(--shadow-color);
}

[data-theme="light"] .header-modern {
  background: white;
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 4px 16px var(--shadow-color);
}

/* Theme Transition Animations */
.theme-transitioning * {
  transition: background-color var(--transition-normal),
              border-color var(--transition-normal),
              color var(--transition-normal),
              box-shadow var(--transition-normal) !important;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .theme-transitioning * {
    transition: none !important;
  }

  .animate-fade-in-up,
  .animate-fade-in-left,
  .animate-fade-in-right,
  .animate-scale-in {
    animation: none !important;
  }
}

/* Theme Toggle Button Styles */
.theme-toggle {
  position: relative;
  overflow: hidden;
}

.theme-toggle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, var(--accent-primary) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  z-index: -1;
}

.theme-toggle:hover::before {
  width: 100px;
  height: 100px;
}

/* Gradient Backgrounds for Light Theme */
[data-theme="light"] .gradient-primary {
  background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
}

[data-theme="light"] .gradient-secondary {
  background: linear-gradient(135deg, #d946ef 0%, #8b5cf6 100%);
}

[data-theme="light"] .gradient-success {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

[data-theme="light"] .gradient-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

[data-theme="light"] .gradient-mesh {
  background:
    radial-gradient(at 40% 20%, rgba(14, 165, 233, 0.3) 0px, transparent 50%),
    radial-gradient(at 80% 0%, rgba(217, 70, 239, 0.3) 0px, transparent 50%),
    radial-gradient(at 0% 50%, rgba(14, 165, 233, 0.2) 0px, transparent 50%),
    radial-gradient(at 80% 50%, rgba(217, 70, 239, 0.2) 0px, transparent 50%),
    linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }

.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }

.transition { transition: all var(--transition-normal); }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }