@echo off
echo ========================================
echo SantriMental Module Installer
echo ========================================
echo.

REM Check if we're in a Laravel project
if not exist "artisan" (
    echo ERROR: This must be run in a Laravel project root directory!
    echo.
    echo Please follow these steps:
    echo 1. composer create-project laravel/laravel my-santrimental
    echo 2. cd my-santrimental
    echo 3. Extract santrimental-modules here
    echo 4. Run this installer again
    echo.
    pause
    exit /b 1
)

echo Installing SantriMental modules to Laravel project...
echo.

echo [1/8] Installing Views...
if not exist "resources\views" mkdir "resources\views"
copy "santrimental-modules\resources\views\*.blade.php" "resources\views\" /Y
echo Views installed successfully!

echo.
echo [2/8] Installing JavaScript modules...
if not exist "public\js" mkdir "public\js"
copy "santrimental-modules\public\js\*.js" "public\js\" /Y
echo JavaScript modules installed successfully!

echo.
echo [3/8] Installing CSS framework...
if not exist "public\css" mkdir "public\css"
copy "santrimental-modules\public\css\*.css" "public\css\" /Y
echo CSS framework installed successfully!

echo.
echo [4/8] Installing Controllers...
if not exist "app\Http\Controllers\Api" mkdir "app\Http\Controllers\Api"
copy "santrimental-modules\app\Http\Controllers\Api\*.php" "app\Http\Controllers\Api\" /Y
echo Controllers installed successfully!

echo.
echo [5/8] Installing Models...
copy "santrimental-modules\app\Models\*.php" "app\Models\" /Y
echo Models installed successfully!

echo.
echo [6/8] Installing Migrations...
copy "santrimental-modules\database\migrations\*.php" "database\migrations\" /Y
echo Migrations installed successfully!

echo.
echo [7/8] Installing Seeders...
copy "santrimental-modules\database\seeders\*.php" "database\seeders\" /Y
echo Seeders installed successfully!

echo.
echo [8/8] Installing Routes and Middleware...
copy "santrimental-modules\routes\web.php" "routes\web.php" /Y
copy "santrimental-modules\routes\api.php" "routes\api.php" /Y
if not exist "app\Http\Middleware" mkdir "app\Http\Middleware"
copy "santrimental-modules\app\Http\Middleware\*.php" "app\Http\Middleware\" /Y
echo Routes and Middleware installed successfully!

echo.
echo ========================================
echo Installation Complete!
echo ========================================
echo.
echo SantriMental modules have been installed successfully!
echo.
echo IMPORTANT: Next steps to complete setup:
echo.
echo 1. Update your .env file with database configuration:
echo    DB_CONNECTION=mysql
echo    DB_HOST=127.0.0.1
echo    DB_PORT=3306
echo    DB_DATABASE=santrimental
echo    DB_USERNAME=root
echo    DB_PASSWORD=
echo.
echo 2. Install Laravel dependencies:
echo    composer install
echo.
echo 3. Generate application key:
echo    php artisan key:generate
echo.
echo 4. Run database migrations:
echo    php artisan migrate
echo.
echo 5. Seed sample data:
echo    php artisan db:seed
echo.
echo 6. Start the development server:
echo    php artisan serve
echo.
echo 7. Access your application:
echo    http://localhost:8000
echo.
echo Default login credentials:
echo - Admin: <EMAIL> / password
echo - Teacher: <EMAIL> / password  
echo - Student: <EMAIL> / password
echo - Parent: <EMAIL> / password
echo.
echo ========================================
echo Happy coding with SantriMental! 🚀
echo ========================================
echo.
pause
