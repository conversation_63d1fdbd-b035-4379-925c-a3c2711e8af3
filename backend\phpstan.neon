includes:
    - ./vendor/larastan/larastan/extension.neon

parameters:
    paths:
        - app/
        - config/
        - database/
        - routes/
    
    # Level of rule strictness (0-9)
    level: 5
    
    ignoreErrors:
        - '#Call to an undefined method Illuminate\\Database\\Eloquent\\Builder::.*#'
        - '#Call to an undefined method Illuminate\\Database\\Query\\Builder::.*#'
        - '#Access to an undefined property App\\Models\\.*::\$.*#'
    
    excludePaths:
        - ./vendor/*
        - ./storage/*
        - ./bootstrap/cache/*
        - ./node_modules/*
    
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
    
    # Laravel specific settings
    laravel:
        container_xml_path: bootstrap/cache/container.xml
        
    # Memory limit
    memoryLimitFile: .phpstan-memory-limit
