<?php

namespace App\Modules\Auth\Services;

use App\Core\Services\BaseService;
use App\Models\Role;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Laravel\Socialite\Facades\Socialite;

class AuthService extends BaseService
{
    /**
     * Register a new user
     */
    public function register(array $data): array
    {
        // Get role
        $role = Role::where('name', $data['role'] ?? 'student')->first();
        if (!$role) {
            throw new \Exception('Invalid role specified');
        }

        // Create user
        $user = User::create([
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'phone' => $data['phone'] ?? null,
            'student_id' => $data['student_id'] ?? null,
            'class' => $data['class'] ?? null,
            'grade' => $data['grade'] ?? null,
            'role_id' => $role->id,
            'is_active' => true,
        ]);

        // Load relationships
        $user->load('role');

        // Fire registered event
        event(new Registered($user));

        // Create token
        $token = $user->createToken('auth_token', ['*'], now()->addDays(30));

        return [
            'user' => $user,
            'token' => $token->plainTextToken,
            'expires_at' => $token->accessToken->expires_at
        ];
    }

    /**
     * Login user
     */
    public function login(array $credentials): ?array
    {
        // Determine login field
        $loginField = filter_var($credentials['login'], FILTER_VALIDATE_EMAIL) ? 'email' : 'student_id';
        
        $authCredentials = [
            $loginField => $credentials['login'],
            'password' => $credentials['password']
        ];

        if (!Auth::attempt($authCredentials)) {
            return null;
        }

        $user = Auth::user();
        
        // Check if user is active
        if (!$user->is_active) {
            Auth::logout();
            throw new \Exception('Account is deactivated');
        }

        // Update last login
        $user->update(['last_login_at' => now()]);

        // Load relationships
        $user->load('role');

        // Create token
        $deviceName = $credentials['device_name'] ?? 'Unknown Device';
        $expiresAt = $credentials['remember'] ?? false ? now()->addDays(30) : now()->addDay();
        
        $token = $user->createToken($deviceName, ['*'], $expiresAt);

        return [
            'user' => $user,
            'token' => $token->plainTextToken,
            'expires_at' => $token->accessToken->expires_at
        ];
    }

    /**
     * Logout user
     */
    public function logout(User $user): void
    {
        // Revoke current token
        $user->currentAccessToken()->delete();
    }

    /**
     * Logout from all devices
     */
    public function logoutFromAllDevices(User $user): void
    {
        // Revoke all tokens
        $user->tokens()->delete();
    }

    /**
     * Get authenticated user with relationships
     */
    public function getAuthenticatedUser(User $user): User
    {
        return $user->load(['role', 'children', 'parents', 'teacher', 'students']);
    }

    /**
     * Refresh token
     */
    public function refreshToken(User $user): array
    {
        // Get current token
        $currentToken = $user->currentAccessToken();
        
        // Create new token
        $newToken = $user->createToken(
            $currentToken->name,
            $currentToken->abilities,
            now()->addDays(30)
        );

        // Revoke old token
        $currentToken->delete();

        return [
            'token' => $newToken->plainTextToken,
            'expires_at' => $newToken->accessToken->expires_at
        ];
    }

    /**
     * Google OAuth authentication
     */
    public function googleAuth(string $accessToken): array
    {
        try {
            // Get user from Google
            $googleUser = Socialite::driver('google')->userFromToken($accessToken);
            
            // Find or create user
            $user = User::where('email', $googleUser->getEmail())->first();
            
            if (!$user) {
                // Create new user
                $role = Role::where('name', 'student')->first();
                
                $names = explode(' ', $googleUser->getName(), 2);
                $firstName = $names[0];
                $lastName = $names[1] ?? '';
                
                $user = User::create([
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'email' => $googleUser->getEmail(),
                    'google_id' => $googleUser->getId(),
                    'avatar' => $googleUser->getAvatar(),
                    'email_verified_at' => now(),
                    'role_id' => $role->id,
                    'is_active' => true,
                ]);
            } else {
                // Update Google ID if not set
                if (!$user->google_id) {
                    $user->update(['google_id' => $googleUser->getId()]);
                }
            }

            // Load relationships
            $user->load('role');

            // Create token
            $token = $user->createToken('google_auth', ['*'], now()->addDays(30));

            return [
                'user' => $user,
                'token' => $token->plainTextToken,
                'expires_at' => $token->accessToken->expires_at
            ];
            
        } catch (\Exception $e) {
            throw new \Exception('Google authentication failed: ' . $e->getMessage());
        }
    }

    /**
     * Send password reset link
     */
    public function sendPasswordResetLink(string $email): void
    {
        $status = Password::sendResetLink(['email' => $email]);

        if ($status !== Password::RESET_LINK_SENT) {
            throw new \Exception('Failed to send password reset link');
        }
    }

    /**
     * Reset password
     */
    public function resetPassword(array $data): bool
    {
        $status = Password::reset(
            $data,
            function ($user, $password) {
                $user->forceFill([
                    'password' => Hash::make($password)
                ])->save();
            }
        );

        return $status === Password::PASSWORD_RESET;
    }

    /**
     * Change password
     */
    public function changePassword(User $user, string $currentPassword, string $newPassword): bool
    {
        if (!Hash::check($currentPassword, $user->password)) {
            return false;
        }

        $user->update([
            'password' => Hash::make($newPassword)
        ]);

        return true;
    }

    /**
     * Verify email
     */
    public function verifyEmail(string $token): bool
    {
        // Implementation depends on your email verification strategy
        // This is a placeholder
        return true;
    }

    /**
     * Resend email verification
     */
    public function resendEmailVerification(User $user): void
    {
        if ($user->hasVerifiedEmail()) {
            throw new \Exception('Email already verified');
        }

        $user->sendEmailVerificationNotification();
    }
}
