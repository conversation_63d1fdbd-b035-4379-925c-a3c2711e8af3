@echo off
echo ========================================
echo   SantriMental - Update Hosts File
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator... OK
) else (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo.
echo Adding santrimental.me to hosts file...

REM Backup current hosts file
copy "C:\Windows\System32\drivers\etc\hosts" "C:\Windows\System32\drivers\etc\hosts.backup.%date:~-4,4%%date:~-10,2%%date:~-7,2%" >nul

REM Check if entry already exists
findstr /C:"santrimental.me" "C:\Windows\System32\drivers\etc\hosts" >nul
if %errorLevel% == 0 (
    echo santrimental.me already exists in hosts file
) else (
    echo 127.0.0.1    santrimental.me >> "C:\Windows\System32\drivers\etc\hosts"
    echo 127.0.0.1    www.santrimental.me >> "C:\Windows\System32\drivers\etc\hosts"
    echo Successfully added santrimental.me to hosts file
)

echo.
echo ========================================
echo   Configuration Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Copy santrimental.conf to C:\laragon\etc\apache2\sites-enabled\
echo 2. Restart Laragon
echo 3. Access: http://santrimental.me
echo.
pause
