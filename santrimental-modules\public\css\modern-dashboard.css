/* Modern Dashboard CSS - Standalone Version */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Color Variables */
:root {
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --secondary-500: #d946ef;
  --secondary-600: #c026d3;
  --success-500: #22c55e;
  --warning-500: #f59e0b;
  --danger-500: #ef4444;
  
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --border-color: #475569;
  
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --border-color: #475569;
}

[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #0f172a;
  --text-secondary: #334155;
  --border-color: #e2e8f0;
}

/* Base Layout */
body {
  background: var(--bg-primary);
  color: var(--text-primary);
  min-height: 100vh;
}

/* Gradient Backgrounds */
.gradient-mesh {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.gradient-primary {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));
}

.gradient-success {
  background: linear-gradient(135deg, var(--success-500), #16a34a);
}

.gradient-warning {
  background: linear-gradient(135deg, var(--warning-500), #d97706);
}

/* Modern Cards */
.modern-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  transition: all var(--transition-normal);
}

.modern-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stats-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
}

/* Sidebar */
.sidebar-modern {
  background: rgba(30, 41, 59, 0.9);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--border-color);
  width: 18rem;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 50;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  margin: 0.25rem 0;
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.sidebar-item:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transform: translateX(4px);
}

.sidebar-item.active {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
}

/* Header */
.header-modern {
  background: rgba(30, 41, 59, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 30;
}

/* Buttons */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn-ghost:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Utility Classes */
.flex { display: flex; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.text-center { text-align: center; }
.text-white { color: white; }
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: 1rem; }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25); }
.w-24 { width: 6rem; }
.h-24 { height: 6rem; }
.w-12 { width: 3rem; }
.h-12 { height: 3rem; }
.w-6 { width: 1.5rem; }
.h-6 { height: 1.5rem; }
.w-5 { width: 1.25rem; }
.h-5 { height: 1.25rem; }
.w-4 { width: 1rem; }
.h-4 { height: 1rem; }
.w-2 { width: 0.5rem; }
.h-2 { height: 0.5rem; }
.max-w-lg { max-width: 32rem; }
.w-full { width: 100%; }
.mx-4 { margin-left: 1rem; margin-right: 1rem; }

/* Grid System */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.gap-4 { gap: 1rem; }

/* Responsive */
@media (max-width: 640px) {
  .grid-cols-2 { grid-template-columns: 1fr; }
  .sidebar-modern { transform: translateX(-100%); }
  .sidebar-modern.open { transform: translateX(0); }
}

/* Loading Animation */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Hover Effects */
.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

.group:hover .group-hover\:text-white {
  color: white;
}

.group:hover .group-hover\:translate-x-1 {
  transform: translateX(0.25rem);
}

.transition-all {
  transition: all var(--transition-normal);
}

.transition-transform {
  transition: transform var(--transition-normal);
}

.duration-300 {
  transition-duration: 300ms;
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

.border-2 {
  border-width: 2px;
}

.border-transparent {
  border-color: transparent;
}

.hover\:border-primary-500:hover {
  border-color: var(--primary-500);
}

.hover\:border-secondary-500:hover {
  border-color: var(--secondary-500);
}

.hover\:border-success-500:hover {
  border-color: var(--success-500);
}

.hover\:border-warning-500:hover {
  border-color: var(--warning-500);
}

/* Text Colors */
.text-white\/70 { color: rgba(255, 255, 255, 0.7); }
.text-white\/60 { color: rgba(255, 255, 255, 0.6); }
.text-white\/40 { color: rgba(255, 255, 255, 0.4); }
.text-green-400 { color: #4ade80; }
.text-blue-400 { color: #60a5fa; }
.text-purple-400 { color: #a78bfa; }

/* Flex utilities */
.flex-1 { flex: 1 1 0%; }
.mr-2 { margin-right: 0.5rem; }
.mr-4 { margin-right: 1rem; }
.ml-auto { margin-left: auto; }

/* Border utilities */
.border-t { border-top-width: 1px; }
.border-white\/10 { border-color: rgba(255, 255, 255, 0.1); }
.pt-6 { padding-top: 1.5rem; }

/* Glass card for light theme */
[data-theme="light"] .modern-card,
[data-theme="light"] .stats-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .gradient-mesh {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

[data-theme="light"] .text-white { color: var(--text-primary); }
[data-theme="light"] .text-white\/70 { color: rgba(15, 23, 42, 0.7); }
[data-theme="light"] .text-white\/60 { color: rgba(15, 23, 42, 0.6); }
[data-theme="light"] .text-white\/40 { color: rgba(15, 23, 42, 0.4); }
