<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FormResponse extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'form_template_id',
        'answers',
        'total_score',
        'status',
        'interpretation',
        'recommendations',
        'completion_time'
    ];

    protected $casts = [
        'answers' => 'array'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function formTemplate()
    {
        return $this->belongsTo(FormTemplate::class);
    }
}
