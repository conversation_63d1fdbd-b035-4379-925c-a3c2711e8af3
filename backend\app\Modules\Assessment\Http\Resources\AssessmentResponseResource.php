<?php

namespace App\Modules\Assessment\Http\Resources;

use App\Core\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class AssessmentResponseResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'form_id' => $this->form_id,
            'status' => $this->status,
            'status_label' => $this->status_label,
            'progress_percentage' => $this->progress_percentage,
            
            // Include answers only for the owner or admin
            'answers' => $this->when(
                $this->canViewAnswers($request),
                $this->answers
            ),
            
            // Score data (only for completed assessments)
            'score_data' => $this->when(
                $this->isCompleted() && $this->canViewResults($request),
                $this->score_data
            ),
            
            // Interpretation (only for completed assessments)
            'interpretation' => $this->when(
                $this->isCompleted() && $this->canViewResults($request),
                $this->interpretation
            ),
            
            // Quick access to key metrics
            'total_score' => $this->when(
                $this->isCompleted() && $this->canViewResults($request),
                $this->total_score
            ),
            'score_percentage' => $this->when(
                $this->isCompleted() && $this->canViewResults($request),
                $this->score_percentage
            ),
            'interpretation_level' => $this->when(
                $this->isCompleted() && $this->canViewResults($request),
                $this->interpretation_level
            ),
            'interpretation_label' => $this->when(
                $this->isCompleted() && $this->canViewResults($request),
                $this->interpretation_label
            ),
            
            // Timing information
            'completion_time' => $this->completion_time,
            'completion_time_human' => $this->completion_time_human,
            'started_at' => $this->started_at?->toISOString(),
            'completed_at' => $this->completed_at?->toISOString(),
            'started_at_human' => $this->humanDate($this->started_at),
            'completed_at_human' => $this->humanDate($this->completed_at),
            
            // Related data
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'first_name' => $this->user->first_name,
                    'last_name' => $this->user->last_name,
                    'email' => $this->user->email,
                ];
            }),
            
            'form' => $this->whenLoaded('form', function () {
                return [
                    'id' => $this->form->id,
                    'code' => $this->form->code,
                    'name' => $this->form->name,
                    'category' => $this->form->category,
                    'questions_count' => $this->form->questions_count,
                ];
            }),
            
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            'created_at_human' => $this->created_at_human,
        ];
    }

    /**
     * Check if user can view answers
     */
    private function canViewAnswers(Request $request): bool
    {
        $user = $request->user();
        
        if (!$user) {
            return false;
        }

        // Owner can always view their answers
        if ($this->user_id === $user->id) {
            return true;
        }

        // Admin can view all answers
        if ($user->isAdmin()) {
            return true;
        }

        // Teachers can view answers of their assigned users
        if ($user->isGuru()) {
            // Add logic to check if user is assigned to this teacher
            return false; // For now, return false
        }

        return false;
    }

    /**
     * Check if user can view results
     */
    private function canViewResults(Request $request): bool
    {
        return $this->canViewAnswers($request);
    }
}
