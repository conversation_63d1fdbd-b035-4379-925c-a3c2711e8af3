<?php

namespace Tests\Feature\Auth;

use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'admin', 'display_name' => 'Administrator', 'permissions' => '[]']);
        Role::create(['name' => 'guru', 'display_name' => 'Guru', 'permissions' => '[]']);
        Role::create(['name' => 'orangtua', 'display_name' => 'Orang Tua', 'permissions' => '[]']);
        Role::create(['name' => 'siswa', 'display_name' => 'Siswa', 'permissions' => '[]']);
    }

    public function test_user_can_register_with_valid_data()
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'phone' => '081234567890',
            'student_id' => 'STD001',
            'class' => '10A',
            'grade' => 10,
            'role' => 'siswa',
            'terms_accepted' => true
        ];

        $response = $this->postJson('/api/auth/register', $userData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user' => [
                            'id',
                            'first_name',
                            'last_name',
                            'email',
                            'role'
                        ],
                        'token',
                        'expires_at'
                    ]
                ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'first_name' => 'John',
            'last_name' => 'Doe'
        ]);
    }

    public function test_user_cannot_register_with_invalid_email()
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => 'invalid-email',
            'password' => 'Password123!',
            'password_confirmation' => 'Password123!',
            'terms_accepted' => true
        ];

        $response = $this->postJson('/api/auth/register', $userData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    public function test_user_cannot_register_with_weak_password()
    {
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => '123',
            'password_confirmation' => '123',
            'terms_accepted' => true
        ];

        $response = $this->postJson('/api/auth/register', $userData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['password']);
    }

    public function test_user_can_login_with_email()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $role->id,
            'is_active' => true
        ]);

        $loginData = [
            'login' => '<EMAIL>',
            'password' => 'password123'
        ];

        $response = $this->postJson('/api/auth/login', $loginData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user',
                        'token',
                        'expires_at'
                    ]
                ]);
    }

    public function test_user_can_login_with_student_id()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'student_id' => 'STD001',
            'password' => Hash::make('password123'),
            'role_id' => $role->id,
            'is_active' => true
        ]);

        $loginData = [
            'login' => 'STD001',
            'password' => 'password123'
        ];

        $response = $this->postJson('/api/auth/login', $loginData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user',
                        'token',
                        'expires_at'
                    ]
                ]);
    }

    public function test_user_cannot_login_with_invalid_credentials()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $role->id,
            'is_active' => true
        ]);

        $loginData = [
            'login' => '<EMAIL>',
            'password' => 'wrongpassword'
        ];

        $response = $this->postJson('/api/auth/login', $loginData);

        $response->assertStatus(401);
    }

    public function test_inactive_user_cannot_login()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $role->id,
            'is_active' => false
        ]);

        $loginData = [
            'login' => '<EMAIL>',
            'password' => 'password123'
        ];

        $response = $this->postJson('/api/auth/login', $loginData);

        $response->assertStatus(422);
    }

    public function test_authenticated_user_can_get_profile()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $role->id,
            'is_active' => true
        ]);

        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/auth/user');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'first_name',
                        'last_name',
                        'email',
                        'role'
                    ]
                ]);
    }

    public function test_authenticated_user_can_logout()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $role->id,
            'is_active' => true
        ]);

        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/auth/logout');

        $response->assertStatus(200);

        // Verify token is revoked
        $this->assertDatabaseMissing('personal_access_tokens', [
            'tokenable_id' => $user->id,
            'name' => 'test-token'
        ]);
    }

    public function test_user_can_refresh_token()
    {
        $role = Role::where('name', 'siswa')->first();
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $role->id,
            'is_active' => true
        ]);

        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/auth/refresh');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'token',
                        'expires_at'
                    ]
                ]);
    }
}
