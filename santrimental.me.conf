# SantriMental Virtual Host Configuration
# Copy this file to: C:\laragon\etc\apache2\sites-enabled\santrimental.me.conf
# Optimized for multi-folder project structure

<VirtualHost *:80>
    ServerName santrimental.me
    ServerAlias www.santrimental.me
    DocumentRoot "C:/laragon/www/santrimental/backend/public"

    <Directory "C:/laragon/www/santrimental/backend/public">
        AllowOverride All
        Require all granted
        DirectoryIndex index.php
        Options -Indexes +FollowSymLinks

        # Laravel URL Rewriting
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [L,QSA]
    </Directory>

    # Security: Block access to sensitive directories
    <Directory "C:/laragon/www/santrimental/backend/storage">
        Require all denied
    </Directory>

    <Directory "C:/laragon/www/santrimental/backend/bootstrap/cache">
        Require all denied
    </Directory>

    # Allow access to DOCS and frontend folders
    Alias /docs "C:/laragon/www/santrimental/DOCS"
    Alias /frontend "C:/laragon/www/santrimental/frontend"

    <Directory "C:/laragon/www/santrimental/DOCS">
        AllowOverride All
        Require all granted
        Options +Indexes +FollowSymLinks
    </Directory>

    <Directory "C:/laragon/www/santrimental/frontend">
        AllowOverride All
        Require all granted
        Options -Indexes +FollowSymLinks
    </Directory>

    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Logging
    ErrorLog "C:/laragon/logs/santrimental_error.log"
    CustomLog "C:/laragon/logs/santrimental_access.log" combined
</VirtualHost>

# Additional virtual host for localhost/santrimental
<VirtualHost *:80>
    ServerName localhost
    DocumentRoot "C:/laragon/www"

    # Alias for santrimental project
    Alias /santrimental "C:/laragon/www/santrimental/backend/public"

    <Directory "C:/laragon/www/santrimental/backend/public">
        AllowOverride All
        Require all granted
        DirectoryIndex index.php
        Options -Indexes +FollowSymLinks

        # Enable URL rewriting
        RewriteEngine On

        # Handle Laravel routes
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [L,QSA]
    </Directory>
</VirtualHost>
