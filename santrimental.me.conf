# Virtual Host configuration for SantriMental
# Place this file in Laragon's apache/sites-enabled directory
# Copy to: C:\laragon\etc\apache2\sites-enabled\santrimental.me.conf

<VirtualHost *:80>
    ServerName santrimental.me
    ServerAlias www.santrimental.me
    DocumentRoot "C:/laragon/www/santrimental/backend/public"

    <Directory "C:/laragon/www/santrimental/backend/public">
        AllowOverride All
        Require all granted
        DirectoryIndex index.php
        Options -Indexes +FollowSymLinks

        # Enable URL rewriting
        RewriteEngine On

        # Handle Laravel routes
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [L,QSA]
    </Directory>

    # Additional alias for accessing without backend/public
    Alias /santrimental "C:/laragon/www/santrimental/backend/public"

    <Directory "C:/laragon/www/santrimental">
        AllowOverride All
        Require all granted
    </Directory>

    ErrorLog "C:/laragon/logs/santrimental_error.log"
    CustomLog "C:/laragon/logs/santrimental_access.log" combined
</VirtualHost>

# Additional virtual host for localhost/santrimental
<VirtualHost *:80>
    ServerName localhost
    DocumentRoot "C:/laragon/www"

    # Alias for santrimental project
    Alias /santrimental "C:/laragon/www/santrimental/backend/public"

    <Directory "C:/laragon/www/santrimental/backend/public">
        AllowOverride All
        Require all granted
        DirectoryIndex index.php
        Options -Indexes +FollowSymLinks

        # Enable URL rewriting
        RewriteEngine On

        # Handle Laravel routes
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [L,QSA]
    </Directory>
</VirtualHost>
