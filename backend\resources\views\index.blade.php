<!DOCTYPE html>
<html lang="id" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SantriMental - Mental Health Assessment</title>
    
    <!-- Modern CSS Framework -->
    @vite(['resources/css/app.css'])
    
    <!-- External Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body class="gradient-mesh min-h-screen flex items-center justify-center">
    
    <!-- Main Container -->
    <div class="max-w-lg w-full mx-4">
        
        <!-- Logo and Title -->
        <div class="text-center mb-8 animate-fade-in-up">
            <div class="w-24 h-24 gradient-primary rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
                <i data-lucide="brain" class="w-12 h-12 text-white"></i>
            </div>
            <h1 class="text-4xl font-bold text-white mb-3">SantriMental</h1>
            <p class="text-white/70 text-lg">Platform Asesmen Kesehatan Mental</p>
            <div class="flex items-center justify-center mt-4 space-x-2">
                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span class="text-sm text-green-400 font-medium">System Online</span>
            </div>
        </div>
        
        <!-- Dashboard Testing Panel -->
        <div class="modern-card p-8 animate-fade-in-up" style="animation-delay: 0.2s;">
            <div class="text-center mb-6">
                <h2 class="text-2xl font-bold text-white mb-2">Dashboard Testing</h2>
                <p class="text-white/60">Authentication temporarily disabled for testing</p>
            </div>
            
            <!-- Dashboard Links -->
            <div class="grid grid-cols-1 gap-4">
                <a href="/dashboard" class="group modern-card p-4 hover:scale-105 transition-all duration-300 border-2 border-transparent hover:border-primary-500">
                    <div class="flex items-center">
                        <div class="p-3 gradient-primary rounded-xl mr-4 group-hover:scale-110 transition-transform">
                            <i data-lucide="graduation-cap" class="w-6 h-6 text-white"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-white">Dashboard Siswa</h3>
                            <p class="text-sm text-white/60">Student mental health dashboard</p>
                        </div>
                        <i data-lucide="arrow-right" class="w-5 h-5 text-white/40 group-hover:text-white group-hover:translate-x-1 transition-all"></i>
                    </div>
                </a>
                
                <a href="/admin/dashboard" class="group modern-card p-4 hover:scale-105 transition-all duration-300 border-2 border-transparent hover:border-secondary-500">
                    <div class="flex items-center">
                        <div class="p-3 gradient-secondary rounded-xl mr-4 group-hover:scale-110 transition-transform">
                            <i data-lucide="shield-check" class="w-6 h-6 text-white"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-white">Admin Dashboard</h3>
                            <p class="text-sm text-white/60">Administrative control panel</p>
                        </div>
                        <i data-lucide="arrow-right" class="w-5 h-5 text-white/40 group-hover:text-white group-hover:translate-x-1 transition-all"></i>
                    </div>
                </a>
                
                <a href="/guru/dashboard" class="group modern-card p-4 hover:scale-105 transition-all duration-300 border-2 border-transparent hover:border-success-500">
                    <div class="flex items-center">
                        <div class="p-3 gradient-success rounded-xl mr-4 group-hover:scale-110 transition-transform">
                            <i data-lucide="user-check" class="w-6 h-6 text-white"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-white">Dashboard Guru</h3>
                            <p class="text-sm text-white/60">Teacher monitoring interface</p>
                        </div>
                        <i data-lucide="arrow-right" class="w-5 h-5 text-white/40 group-hover:text-white group-hover:translate-x-1 transition-all"></i>
                    </div>
                </a>
                
                <a href="/orangtua/dashboard" class="group modern-card p-4 hover:scale-105 transition-all duration-300 border-2 border-transparent hover:border-warning-500">
                    <div class="flex items-center">
                        <div class="p-3 gradient-warning rounded-xl mr-4 group-hover:scale-110 transition-transform">
                            <i data-lucide="heart" class="w-6 h-6 text-white"></i>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-white">Dashboard Orang Tua</h3>
                            <p class="text-sm text-white/60">Parent monitoring dashboard</p>
                        </div>
                        <i data-lucide="arrow-right" class="w-5 h-5 text-white/40 group-hover:text-white group-hover:translate-x-1 transition-all"></i>
                    </div>
                </a>
            </div>
            
            <!-- Additional Testing Links -->
            <div class="mt-6 pt-6 border-t border-white/10">
                <h3 class="text-lg font-semibold text-white mb-4">Additional Features</h3>
                <div class="grid grid-cols-2 gap-3">
                    <a href="/assessments" class="btn-ghost text-center py-3">
                        <i data-lucide="clipboard-list" class="w-5 h-5 mx-auto mb-1"></i>
                        <span class="text-sm">Assessments</span>
                    </a>
                    <a href="/history" class="btn-ghost text-center py-3">
                        <i data-lucide="history" class="w-5 h-5 mx-auto mb-1"></i>
                        <span class="text-sm">History</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Theme Toggle -->
        <div class="text-center mt-6">
            <button id="theme-toggle" class="btn-ghost p-3 rounded-xl" data-tooltip="Toggle Theme">
                <i data-lucide="moon" class="w-5 h-5 mr-2"></i>
                <span class="text-sm">Toggle Theme</span>
            </button>
        </div>
        
        <!-- Status Info -->
        <div class="text-center mt-4 animate-fade-in-up" style="animation-delay: 0.4s;">
            <div class="glass-card p-4 rounded-xl">
                <p class="text-white/60 text-sm mb-2">🚀 Modern Dashboard Implementation</p>
                <div class="flex items-center justify-center space-x-4 text-xs">
                    <span class="flex items-center">
                        <div class="w-2 h-2 bg-green-400 rounded-full mr-1"></div>
                        CSS Framework
                    </span>
                    <span class="flex items-center">
                        <div class="w-2 h-2 bg-blue-400 rounded-full mr-1"></div>
                        Responsive Design
                    </span>
                    <span class="flex items-center">
                        <div class="w-2 h-2 bg-purple-400 rounded-full mr-1"></div>
                        Dark/Light Theme
                    </span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript Libraries -->
    <script src="{{ asset('js/auth.js') }}"></script>
    <script src="{{ asset('js/modern-dashboard.js') }}"></script>
    <script src="{{ asset('js/modern-components.js') }}"></script>
    
    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        document.addEventListener('DOMContentLoaded', () => {
            // Show welcome notification
            setTimeout(() => {
                if (window.modernComponents) {
                    window.modernComponents.showNotification(
                        'Welcome to SantriMental!',
                        'success',
                        4000,
                        {
                            description: 'Modern dashboard is ready for testing. Authentication is temporarily disabled.'
                        }
                    );
                }
            }, 1000);
            
            // Add hover effects to dashboard links
            const dashboardLinks = document.querySelectorAll('a[href*="dashboard"]');
            dashboardLinks.forEach(link => {
                link.addEventListener('mouseenter', () => {
                    link.style.transform = 'translateY(-2px)';
                });
                
                link.addEventListener('mouseleave', () => {
                    link.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
