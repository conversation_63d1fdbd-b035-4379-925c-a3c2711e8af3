<?php

use Illuminate\Support\Facades\Route;
use App\Modules\Auth\Http\Controllers\Api\AuthController;

/*
|--------------------------------------------------------------------------
| Auth API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the Auth module.
| These routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group.
|
*/

// Public authentication routes
Route::prefix('auth')->group(function () {
    // Registration and login
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);
    
    // OAuth routes
    Route::post('/google', [AuthController::class, 'googleAuth']);
    
    // Password reset routes
    Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('/reset-password', [AuthController::class, 'resetPassword']);
    
    // Email verification routes
    Route::post('/verify-email', [AuthController::class, 'verifyEmail']);
});

// Protected authentication routes
Route::middleware('auth:sanctum')->prefix('auth')->group(function () {
    // User management
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/refresh', [AuthController::class, 'refresh']);
    
    // Password management
    Route::post('/change-password', [AuthController::class, 'changePassword']);
    
    // Email verification
    Route::post('/resend-verification', [AuthController::class, 'resendVerification']);
});
