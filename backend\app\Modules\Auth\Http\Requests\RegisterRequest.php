<?php

namespace App\Modules\Auth\Http\Requests;

use App\Core\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Password;

class RegisterRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'first_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s]+$/'
            ],
            'last_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s]+$/'
            ],
            'email' => [
                'required',
                'string',
                'email:rfc,dns',
                'max:255',
                'unique:users,email'
            ],
            'password' => [
                'required',
                'string',
                'confirmed',
                Password::min(8)
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
            ],
            'phone' => [
                'nullable',
                'string',
                'regex:/^(\+62|62|0)[0-9]{9,13}$/'
            ],
            'student_id' => [
                'nullable',
                'string',
                'max:50',
                'unique:users,student_id'
            ],
            'class' => [
                'nullable',
                'string',
                'max:100'
            ],
            'grade' => [
                'nullable',
                'integer',
                'min:1',
                'max:12'
            ],
            'role' => [
                'nullable',
                'string',
                'in:student,admin'
            ],
            'terms_accepted' => [
                'required',
                'boolean',
                'accepted'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'Nama depan wajib diisi.',
            'first_name.regex' => 'Nama depan hanya boleh berisi huruf dan spasi.',
            'last_name.required' => 'Nama belakang wajib diisi.',
            'last_name.regex' => 'Nama belakang hanya boleh berisi huruf dan spasi.',
            'email.required' => 'Email wajib diisi.',
            'email.email' => 'Format email tidak valid.',
            'email.unique' => 'Email sudah terdaftar.',
            'password.required' => 'Password wajib diisi.',
            'password.confirmed' => 'Konfirmasi password tidak cocok.',
            'password.min' => 'Password minimal 8 karakter.',
            'phone.regex' => 'Format nomor telepon tidak valid.',
            'student_id.unique' => 'NIS sudah terdaftar.',
            'grade.min' => 'Kelas minimal 1.',
            'grade.max' => 'Kelas maksimal 12.',
            'role.in' => 'Role tidak valid.',
            'terms_accepted.accepted' => 'Anda harus menyetujui syarat dan ketentuan.'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'first_name' => 'nama depan',
            'last_name' => 'nama belakang',
            'email' => 'email',
            'password' => 'password',
            'phone' => 'nomor telepon',
            'student_id' => 'NIS',
            'class' => 'kelas',
            'grade' => 'tingkat',
            'role' => 'peran',
            'terms_accepted' => 'persetujuan syarat dan ketentuan'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'first_name' => trim($this->first_name),
            'last_name' => trim($this->last_name),
            'email' => strtolower(trim($this->email)),
            'phone' => $this->phone ? preg_replace('/[^0-9+]/', '', $this->phone) : null,
            'student_id' => $this->student_id ? trim($this->student_id) : null,
            'class' => $this->class ? trim($this->class) : null,
            'role' => $this->role ?? 'siswa', // Default role
            'terms_accepted' => filter_var($this->terms_accepted, FILTER_VALIDATE_BOOLEAN)
        ]);
    }
}
