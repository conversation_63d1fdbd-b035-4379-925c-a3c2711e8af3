/**
 * Dynamic Form Logic - Handles any form based on configuration
 */
document.addEventListener('DOMContentLoaded', () => {
    const auth = window.auth;
    const Utils = window.Utils;
    const formCode = window.formCode;

    // Check authentication
    if (!auth.isAuthenticated()) {
        window.location.href = '/';
        return;
    }

    // Elements
    const loadingState = document.getElementById('loading-state');
    const errorState = document.getElementById('error-state');
    const formSection = document.getElementById('form-section');
    const resultSection = document.getElementById('result-section');
    const progressSection = document.getElementById('progress-section');
    
    const formTitle = document.getElementById('form-title');
    const formDescription = document.getElementById('form-description');
    const formInstructions = document.getElementById('form-instructions');
    const formCategory = document.getElementById('form-category');
    const questionsContainer = document.getElementById('questions-container');
    const assessmentForm = document.getElementById('assessmentForm');
    
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const alertMessage = document.getElementById('alert-message');
    const submitBtn = document.getElementById('submit-btn');
    const submitText = document.getElementById('submit-text');
    const submitLoading = document.getElementById('submit-loading');
    
    // Result elements
    const resultTitle = document.getElementById('result-title');
    const resultIcon = document.getElementById('result-icon');
    const scoreEl = document.getElementById('score');
    const scoreRange = document.getElementById('score-range');
    const interpretationEl = document.getElementById('interpretation');
    const recommendationEl = document.getElementById('recommendation');
    const stepsListEl = document.getElementById('steps-list');
    const saveResultBtn = document.getElementById('save-result-btn');
    const resetButton = document.getElementById('reset-button');

    let currentForm = null;
    let currentResult = null;
    let startTime = Date.now();

    // Load form configuration
    async function loadForm() {
        try {
            const response = await Utils.apiCall(`/forms/${formCode}`);
            
            if (response.success) {
                currentForm = response.data;
                setupForm();
                showForm();
            } else {
                showError();
            }
        } catch (error) {
            console.error('Failed to load form:', error);
            showError();
        }
    }

    // Setup form based on configuration
    function setupForm() {
        formTitle.textContent = currentForm.name;
        formDescription.textContent = currentForm.description;
        formCategory.textContent = currentForm.category.replace('_', ' ');
        resultTitle.textContent = `Hasil ${currentForm.name}`;
        
        // Setup instructions based on form type
        const instructions = getFormInstructions(currentForm.code);
        formInstructions.querySelector('p').textContent = instructions;
        
        // Render questions
        renderQuestions();
        
        // Setup progress
        updateProgress();
    }

    // Get form-specific instructions
    function getFormInstructions(code) {
        const instructionsMap = {
            'SRQ20': 'Jawablah pertanyaan berikut berdasarkan apa yang Anda rasakan dalam 30 hari terakhir. Pilih "Ya" jika Anda mengalami gejala tersebut, atau "Tidak" jika tidak mengalaminya.',
            'GSE': 'Untuk setiap pernyataan, pilih angka yang paling sesuai dengan keyakinan Anda. 1 = Tidak benar sama sekali, 2 = Hampir tidak benar, 3 = Agak benar, 4 = Benar sekali.',
            'MSCS': 'Untuk setiap pernyataan, pilih angka yang menggambarkan seberapa sesuai dengan kondisi Anda. 1 = Sangat tidak setuju, 7 = Sangat setuju.',
            'MHKQ': 'Pilih "Benar" atau "Salah" untuk setiap pernyataan berdasarkan pengetahuan Anda tentang kesehatan jiwa.',
            'DASS42': 'Bacalah setiap pernyataan dan pilih angka yang paling menggambarkan keadaan Anda selama satu minggu terakhir. 0 = Tidak sesuai sama sekali, 1 = Kadang-kadang, 2 = Lumayan sering, 3 = Sering sekali.'
        };

        return instructionsMap[code] || 'Jawablah semua pertanyaan dengan jujur sesuai kondisi Anda.';
    }

    // Render questions based on form configuration
    function renderQuestions() {
        let questionsHTML = '';
        
        currentForm.questions.forEach((question, index) => {
            const questionNumber = index + 1;
            questionsHTML += `
                <div class="question-card glass-card p-6 rounded-xl">
                    <div class="flex items-start justify-between">
                        <div class="flex-1 mr-6">
                            <div class="flex items-center mb-3">
                                <span class="bg-purple-500 text-white text-sm font-bold rounded-full w-8 h-8 flex items-center justify-center mr-3">${questionNumber}</span>
                                <span class="text-purple-200 text-sm">Pertanyaan ${questionNumber} dari ${currentForm.questions.length}</span>
                            </div>
                            <p class="text-white font-medium text-lg leading-relaxed">${question}</p>
                        </div>
                        <div class="flex flex-col space-y-3">
                            ${renderQuestionOptions(questionNumber)}
                        </div>
                    </div>
                </div>
            `;
        });
        
        questionsContainer.innerHTML = questionsHTML;
        
        // Add event listeners for progress tracking
        const radioInputs = questionsContainer.querySelectorAll('input[type="radio"]');
        radioInputs.forEach(input => {
            input.addEventListener('change', updateProgress);
        });
    }

    // Render question options based on form type
    function renderQuestionOptions(questionNumber) {
        const scoringRules = currentForm.scoring_rules;

        if (scoringRules.type === 'binary_sum') {
            // Yes/No questions (like SRQ-20)
            return `
                <div class="flex items-center">
                    <input type="radio" id="q${questionNumber}-yes" name="q${questionNumber}" value="1" class="sr-only" required>
                    <label for="q${questionNumber}-yes" class="custom-radio-label cursor-pointer w-20 text-center py-3 px-4 border-2 border-white/30 rounded-lg text-white hover:border-purple-400 transition-all duration-200">Ya</label>
                </div>
                <div class="flex items-center">
                    <input type="radio" id="q${questionNumber}-no" name="q${questionNumber}" value="0" class="sr-only" required>
                    <label for="q${questionNumber}-no" class="custom-radio-label cursor-pointer w-20 text-center py-3 px-4 border-2 border-white/30 rounded-lg text-white hover:border-purple-400 transition-all duration-200">Tidak</label>
                </div>
            `;
        } else if (scoringRules.type === 'likert_sum') {
            // Likert scale questions (like GSE, MSCS)
            const questionRule = scoringRules.questions[1]; // Assuming all questions have same scale
            const options = [];

            for (let i = questionRule.min; i <= questionRule.max; i++) {
                options.push(`
                    <div class="flex items-center">
                        <input type="radio" id="q${questionNumber}-${i}" name="q${questionNumber}" value="${i}" class="sr-only" required>
                        <label for="q${questionNumber}-${i}" class="custom-scale-label cursor-pointer w-12 h-12 text-center flex items-center justify-center border-2 border-white/30 rounded-lg text-white hover:border-purple-400 transition-all duration-200">${i}</label>
                    </div>
                `);
            }

            return options.join('');
        } else if (scoringRules.type === 'knowledge_sum') {
            // True/False questions (like MHKQ)
            return `
                <div class="flex items-center">
                    <input type="radio" id="q${questionNumber}-benar" name="q${questionNumber}" value="benar" class="sr-only" required>
                    <label for="q${questionNumber}-benar" class="custom-radio-label cursor-pointer w-24 text-center py-3 px-4 border-2 border-white/30 rounded-lg text-white hover:border-purple-400 transition-all duration-200">Benar</label>
                </div>
                <div class="flex items-center">
                    <input type="radio" id="q${questionNumber}-salah" name="q${questionNumber}" value="salah" class="sr-only" required>
                    <label for="q${questionNumber}-salah" class="custom-radio-label cursor-pointer w-24 text-center py-3 px-4 border-2 border-white/30 rounded-lg text-white hover:border-purple-400 transition-all duration-200">Salah</label>
                </div>
            `;
        } else if (scoringRules.type === 'dass_scale') {
            // DASS scale (0-3)
            const labels = ['Tidak sesuai', 'Kadang-kadang', 'Lumayan sering', 'Sering sekali'];
            const options = [];

            for (let i = 0; i <= 3; i++) {
                options.push(`
                    <div class="flex flex-col items-center">
                        <input type="radio" id="q${questionNumber}-${i}" name="q${questionNumber}" value="${i}" class="sr-only" required>
                        <label for="q${questionNumber}-${i}" class="custom-scale-label cursor-pointer w-16 h-16 text-center flex items-center justify-center border-2 border-white/30 rounded-lg text-white hover:border-purple-400 transition-all duration-200 mb-2">
                            <span class="font-bold text-lg">${i}</span>
                        </label>
                        <span class="text-xs text-purple-200 text-center">${labels[i]}</span>
                    </div>
                `);
            }

            return options.join('');
        }

        // Default fallback
        return `
            <div class="flex items-center">
                <input type="radio" id="q${questionNumber}-yes" name="q${questionNumber}" value="1" class="sr-only" required>
                <label for="q${questionNumber}-yes" class="custom-radio-label cursor-pointer w-20 text-center py-3 px-4 border-2 border-white/30 rounded-lg text-white hover:border-purple-400 transition-all duration-200">Ya</label>
            </div>
            <div class="flex items-center">
                <input type="radio" id="q${questionNumber}-no" name="q${questionNumber}" value="0" class="sr-only" required>
                <label for="q${questionNumber}-no" class="custom-radio-label cursor-pointer w-20 text-center py-3 px-4 border-2 border-white/30 rounded-lg text-white hover:border-purple-400 transition-all duration-200">Tidak</label>
            </div>
        `;
    }

    // Update progress bar
    function updateProgress() {
        const answeredQuestions = new Set();
        const formData = new FormData(assessmentForm);
        
        for (let key of formData.keys()) {
            answeredQuestions.add(key);
        }

        const progress = (answeredQuestions.size / currentForm.questions.length) * 100;
        progressBar.style.width = `${progress}%`;
        progressText.textContent = `${answeredQuestions.size}/${currentForm.questions.length}`;
    }

    // Handle form submission
    assessmentForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const formData = new FormData(assessmentForm);
        const answeredQuestions = Array.from(formData.keys()).length;

        // Check if all questions are answered
        if (answeredQuestions < currentForm.questions.length) {
            alertMessage.classList.remove('hidden');
            window.scrollTo({ top: 0, behavior: 'smooth' });
            return;
        }
        
        alertMessage.classList.add('hidden');
        showLoading();

        try {
            // Prepare answers
            const answers = {};
            for (let [key, value] of formData.entries()) {
                if (key !== '_token') {
                    const questionNumber = parseInt(key.replace('q', ''));
                    // Handle different answer types
                    if (currentForm.scoring_rules.type === 'knowledge_sum') {
                        answers[questionNumber] = value; // Keep as string for true/false
                    } else {
                        answers[questionNumber] = parseInt(value);
                    }
                }
            }

            const completionTime = Math.floor((Date.now() - startTime) / 1000);

            // Submit to API
            const response = await Utils.apiCall(`/forms/${formCode}/submit`, {
                method: 'POST',
                body: JSON.stringify({
                    answers: answers,
                    completion_time: completionTime
                })
            });

            if (response.success) {
                currentResult = response.data;
                displayResult();
            } else {
                throw new Error(response.message || 'Gagal memproses form');
            }
            
        } catch (error) {
            console.error('Error processing form:', error);
            Utils.showNotification('Terjadi kesalahan saat memproses form', 'error');
        } finally {
            hideLoading();
        }
    });

    // Display result
    function displayResult() {
        const { total_score, status, interpretation, recommendations } = currentResult;
        
        scoreEl.textContent = total_score;
        scoreRange.textContent = `dari ${currentForm.scoring_rules.max_score || currentForm.questions.length} poin`;
        interpretationEl.textContent = interpretation;
        
        // Set icon and colors based on status
        const statusConfig = getStatusConfig(status);
        resultIcon.innerHTML = statusConfig.icon;
        resultIcon.className = `w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center ${statusConfig.bgClass}`;
        
        // Display recommendations
        if (Array.isArray(recommendations)) {
            stepsListEl.innerHTML = recommendations.map(rec => `<li class="text-left">• ${rec}</li>`).join('');
        } else {
            stepsListEl.innerHTML = `<li class="text-left">• ${recommendations}</li>`;
        }

        // Show result section
        formSection.classList.add('hidden');
        progressSection.classList.add('hidden');
        resultSection.classList.remove('hidden');
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // Get status configuration
    function getStatusConfig(status) {
        const configs = {
            'normal': {
                icon: `<svg class="w-12 h-12 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>`,
                bgClass: 'bg-green-500/20 border border-green-400'
            },
            'concern': {
                icon: `<svg class="w-12 h-12 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>`,
                bgClass: 'bg-yellow-500/20 border border-yellow-400'
            },
            'high_risk': {
                icon: `<svg class="w-12 h-12 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>`,
                bgClass: 'bg-red-500/20 border border-red-400'
            },
            'low': {
                icon: `<svg class="w-12 h-12 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>`,
                bgClass: 'bg-red-500/20 border border-red-400'
            },
            'moderate': {
                icon: `<svg class="w-12 h-12 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>`,
                bgClass: 'bg-yellow-500/20 border border-yellow-400'
            },
            'high': {
                icon: `<svg class="w-12 h-12 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>`,
                bgClass: 'bg-green-500/20 border border-green-400'
            }
        };
        
        return configs[status] || configs['normal'];
    }

    // Show/hide states
    function showForm() {
        loadingState.classList.add('hidden');
        errorState.classList.add('hidden');
        formSection.classList.remove('hidden');
        progressSection.classList.remove('hidden');
    }

    function showError() {
        loadingState.classList.add('hidden');
        formSection.classList.add('hidden');
        progressSection.classList.add('hidden');
        resultSection.classList.add('hidden');
        errorState.classList.remove('hidden');
    }

    function showLoading() {
        submitBtn.disabled = true;
        submitText.textContent = 'Memproses...';
        submitLoading.classList.remove('hidden');
    }

    function hideLoading() {
        submitBtn.disabled = false;
        submitText.textContent = 'Lihat Hasil';
        submitLoading.classList.add('hidden');
    }

    // Event listeners
    saveResultBtn.addEventListener('click', () => {
        Utils.showNotification('Hasil berhasil disimpan!', 'success');
        setTimeout(() => {
            window.location.href = '/dashboard';
        }, 1500);
    });

    resetButton.addEventListener('click', () => {
        if (confirm('Apakah Anda yakin ingin mengulang assessment? Data yang sudah diisi akan hilang.')) {
            location.reload();
        }
    });

    // Initialize
    loadForm();
});
