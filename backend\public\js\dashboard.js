/**
 * Dashboard Page Logic with API integration
 */
document.addEventListener('DOMContentLoaded', () => {
    const auth = window.auth;
    const Utils = window.Utils;

    if (!auth.isAuthenticated()) {
        window.location.href = '/';
        return;
    }

    // Elements
    const userNameEl = document.getElementById('user-name');
    const userEmailEl = document.getElementById('user-email');
    const userInitialsEl = document.getElementById('user-initials');
    const logoutBtn = document.getElementById('logout-btn');
    const sidebarLinks = document.querySelectorAll('.sidebar-link');
    const pageTitle = document.getElementById('page-title');

    const pages = {
        dashboard: document.getElementById('dashboard-content'),
        assessment: document.getElementById('assessment-page'),
        history: document.getElementById('history-page'),
        analytics: document.getElementById('analytics-page'),
        profile: document.getElementById('profile-page')
    };

    // Show user info
    const user = auth.getCurrentUser();
    if (user) {
        userNameEl.textContent = `${user.firstName} ${user.lastName}`;
        userEmailEl.textContent = user.email;
        userInitialsEl.textContent = Utils.getInitials(`${user.firstName} ${user.lastName}`);
    } else {
        userNameEl.textContent = 'Demo User';
        userEmailEl.textContent = '<EMAIL>';
        userInitialsEl.textContent = 'DU';
    }

    // Sidebar navigation
    sidebarLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const page = link.getAttribute('data-page');
            if (!page || !pages[page]) return;

            // Update active link
            sidebarLinks.forEach(l => l.classList.remove('sidebar-active'));
            link.classList.add('sidebar-active');

            // Update page title
            pageTitle.textContent = link.textContent.trim();

            // Show selected page, hide others
            Object.entries(pages).forEach(([key, el]) => {
                if (key === page) {
                    el.classList.remove('hidden');
                } else {
                    el.classList.add('hidden');
                }
            });

            // Load page-specific data
            if (page === 'dashboard') {
                loadDashboardData();
            }
        });
    });

    // Logout button
    logoutBtn.addEventListener('click', () => {
        auth.logout();
    });

    // Mobile menu toggle
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const sidebar = document.getElementById('sidebar');
    const mobileOverlay = document.getElementById('mobile-overlay');

    mobileMenuBtn.addEventListener('click', () => {
        sidebar.classList.toggle('mobile-menu-open');
        sidebar.classList.toggle('mobile-menu-closed');
        mobileOverlay.classList.toggle('hidden');
    });

    mobileOverlay.addEventListener('click', () => {
        sidebar.classList.add('mobile-menu-closed');
        sidebar.classList.remove('mobile-menu-open');
        mobileOverlay.classList.add('hidden');
    });

    // Load user role and permissions
    async function loadUserRole() {
        try {
            const response = await Utils.apiCall('/user/role');
            if (response.success) {
                window.userRole = response.data;
                setupRoleBasedUI(response.data);
                return response.data;
            }
        } catch (error) {
            console.error('Failed to load user role:', error);
        }
        return null;
    }

    // Setup UI based on user role
    function setupRoleBasedUI(roleData) {
        const { role, user } = roleData;

        // Check if we should redirect to role-specific dashboard
        const currentPath = window.location.pathname;
        if (currentPath === '/dashboard') {
            switch (role.name) {
                case 'admin':
                    window.location.href = '/admin/dashboard';
                    return;
                case 'guru':
                    window.location.href = '/guru/dashboard';
                    return;
                case 'orangtua':
                    window.location.href = '/orangtua/dashboard';
                    return;
                case 'siswa':
                    // Siswa tetap di dashboard utama
                    break;
            }
        }

        // Update header with role info
        const headerRole = document.querySelector('.text-purple-200');
        if (headerRole) {
            headerRole.textContent = `${role.display_name} - ${user.first_name} ${user.last_name}`;
        }

        // Setup role-specific menu
        setupRoleSpecificMenu(role);

        // Hide/show menu items based on role
        setupMenuPermissions(role);
    }

    // Setup role-specific menu items
    function setupRoleSpecificMenu(role) {
        const roleMenuContainer = document.getElementById('role-specific-menu');
        if (!roleMenuContainer) return;

        let menuItems = [];

        switch (role.name) {
            case 'admin':
                menuItems = [
                    { name: 'Kelola Pengguna', icon: '👥', page: 'users' },
                    { name: 'Kelola Assessment', icon: '📋', page: 'manage-assessments' },
                    { name: 'Laporan', icon: '📊', page: 'reports' },
                    { name: 'Pengaturan', icon: '⚙️', page: 'settings' }
                ];
                break;
            case 'guru':
                menuItems = [
                    { name: 'Daftar Siswa', icon: '👨‍🎓', page: 'students' },
                    { name: 'Hasil Assessment', icon: '📋', page: 'student-assessments' },
                    { name: 'Laporan Kelas', icon: '📊', page: 'class-reports' }
                ];
                break;
            case 'orangtua':
                menuItems = [
                    { name: 'Anak Saya', icon: '👶', page: 'children' },
                    { name: 'Hasil Assessment', icon: '📋', page: 'child-assessments' }
                ];
                break;
            case 'siswa':
                // Siswa hanya memiliki menu default
                break;
        }

        const menuHTML = menuItems.map(item => `
            <a href="#" data-page="${item.page}" class="sidebar-link flex items-center px-4 py-3 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-300">
                <span class="w-5 h-5 mr-3 text-center">${item.icon}</span>
                ${item.name}
            </a>
        `).join('');

        roleMenuContainer.innerHTML = menuHTML;
    }

    // Setup menu permissions based on role
    function setupMenuPermissions(role) {
        const assessmentMenu = document.getElementById('assessment-menu');
        const historyMenu = document.getElementById('history-menu');

        // Only siswa can access assessments
        if (role.name !== 'siswa' && assessmentMenu) {
            assessmentMenu.style.display = 'none';
        }

        // Adjust history menu based on role
        if (historyMenu && role.name !== 'siswa') {
            historyMenu.querySelector('span').textContent = 'Hasil Assessment';
        }
    }

    // Dashboard data loading
    async function loadDashboardData() {
        try {
            const data = await Utils.apiCall('/dashboard/role-data');

            // Update stats cards
            document.getElementById('total-assessments').textContent = data.total_assessments || 0;
            document.getElementById('latest-score').textContent = data.latest_assessment?.total_score || data.latest_assessment?.score || '-';
            document.getElementById('latest-status').textContent = getStatusText(data.latest_assessment?.status);
            document.getElementById('monthly-count').textContent = data.monthly_stats?.total_assessments || 0;

            // Update status color
            const statusEl = document.getElementById('latest-status');
            const status = data.latest_assessment?.status;
            statusEl.className = `text-xl font-semibold ${getStatusColor(status)}`;

            // Load charts
            loadScoreTrendChart(data.trend || []);
            loadMonthlyChart(data.monthly_stats || {});
            loadRecentActivity(data.recent_activity || []);

        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            Utils.showNotification('Gagal memuat data dashboard', 'error');
        }
    }



    function getStatusText(status) {
        const statusMap = {
            'normal': 'Normal',
            'concern': 'Perlu Perhatian',
            'high_risk': 'Risiko Tinggi'
        };
        return statusMap[status] || 'Belum Ada Data';
    }

    function getStatusColor(status) {
        const colorMap = {
            'normal': 'text-green-400',
            'concern': 'text-yellow-400',
            'high_risk': 'text-red-400'
        };
        return colorMap[status] || 'text-gray-400';
    }

    // Chart functions
    function loadScoreTrendChart(trendData) {
        const ctx = document.getElementById('score-trend-chart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: trendData.map(item => Utils.formatDate(item.date, { month: 'short', day: 'numeric' })),
                datasets: [{
                    label: 'Skor SRQ-20',
                    data: trendData.map(item => item.score),
                    borderColor: 'rgb(167, 139, 250)',
                    backgroundColor: 'rgba(167, 139, 250, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: 'white'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.7)'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });
    }

    function loadMonthlyChart(monthlyData) {
        const ctx = document.getElementById('monthly-chart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Normal', 'Perlu Perhatian', 'Risiko Tinggi'],
                datasets: [{
                    data: [
                        monthlyData.normal || 0,
                        monthlyData.concerns || 0,
                        monthlyData.high_risk || 0
                    ],
                    backgroundColor: [
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(234, 179, 8, 0.8)',
                        'rgba(239, 68, 68, 0.8)'
                    ],
                    borderColor: [
                        'rgb(34, 197, 94)',
                        'rgb(234, 179, 8)',
                        'rgb(239, 68, 68)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: 'white'
                        }
                    }
                }
            }
        });
    }

    function loadRecentActivity(activities) {
        const container = document.getElementById('recent-activity');
        if (!container) return;

        if (activities.length === 0) {
            container.innerHTML = '<p class="text-purple-200 text-center">Belum ada aktivitas terbaru</p>';
            return;
        }

        container.innerHTML = activities.map(activity => `
            <div class="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <div>
                        <p class="text-white font-medium">Skrining SRQ-20 Selesai</p>
                        <p class="text-purple-200 text-sm">Skor: ${activity.score} - ${getStatusText(activity.status)}</p>
                    </div>
                </div>
                <span class="text-purple-300 text-sm">${Utils.formatDate(activity.created_at)}</span>
            </div>
        `).join('');
    }

    // Initialize dashboard with role-based setup
    async function initDashboard() {
        await loadUserRole();
        await loadDashboardData();
    }

    // Load initial dashboard data
    initDashboard();
});
