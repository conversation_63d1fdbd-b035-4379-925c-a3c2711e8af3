<?php

namespace App\Core\Traits;

trait HasActiveStatus
{
    /**
     * Scope to get only active records
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only inactive records
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Check if the model is active
     */
    public function isActive(): bool
    {
        return $this->is_active === true;
    }

    /**
     * Check if the model is inactive
     */
    public function isInactive(): bool
    {
        return $this->is_active === false;
    }

    /**
     * Activate the model
     */
    public function activate(): bool
    {
        return $this->update(['is_active' => true]);
    }

    /**
     * Deactivate the model
     */
    public function deactivate(): bool
    {
        return $this->update(['is_active' => false]);
    }

    /**
     * Toggle active status
     */
    public function toggleActive(): bool
    {
        return $this->update(['is_active' => !$this->is_active]);
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return $this->is_active ? 'Active' : 'Inactive';
    }

    /**
     * Get status color
     */
    public function getStatusColorAttribute(): string
    {
        return $this->is_active ? 'green' : 'gray';
    }

    /**
     * Get status badge data
     */
    public function getStatusBadgeAttribute(): array
    {
        return [
            'status' => $this->is_active ? 'active' : 'inactive',
            'label' => $this->status_label,
            'color' => $this->status_color,
        ];
    }
}
