<?php

namespace App\Modules\Auth\Providers;

use App\Modules\Auth\Services\AuthService;
use Illuminate\Support\ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Bind Auth Service
        $this->app->bind(AuthService::class, function ($app) {
            return new AuthService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load module routes
        $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
        
        // Load module views if any
        // $this->loadViewsFrom(__DIR__ . '/../resources/views', 'auth');
        
        // Publish module assets if any
        // $this->publishes([
        //     __DIR__ . '/../resources/assets' => public_path('vendor/auth'),
        // ], 'auth-assets');
    }
}
