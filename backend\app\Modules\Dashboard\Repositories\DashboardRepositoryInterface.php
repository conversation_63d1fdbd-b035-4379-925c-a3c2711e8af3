<?php

namespace App\Modules\Dashboard\Repositories;

interface DashboardRepositoryInterface
{
    /**
     * Get dashboard statistics for a specific role
     */
    public function getDashboardStats(string $role, int $userId): array;

    /**
     * Get recent activities for dashboard
     */
    public function getRecentActivities(int $userId, int $limit = 10): array;

    /**
     * Get trend data for charts
     */
    public function getTrendData(string $role, int $userId, string $period = '6months'): array;

    /**
     * Get role-specific data
     */
    public function getRoleSpecificData(string $role, int $userId): array;

    /**
     * Get monthly statistics
     */
    public function getMonthlyStats(int $userId, string $role): array;

    /**
     * Get assessment summary
     */
    public function getAssessmentSummary(int $userId, string $role): array;
}
