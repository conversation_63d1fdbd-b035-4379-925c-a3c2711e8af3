version: '3.8'

services:
  # Laravel Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: santrimental_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - santrimental
    depends_on:
      - db
      - redis
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_HOST=db
      - DB_DATABASE=santrimental
      - DB_USERNAME=santrimental
      - DB_PASSWORD=secret
      - REDIS_HOST=redis

  # Nginx Web Server
  webserver:
    image: nginx:alpine
    container_name: santrimental_webserver
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./:/var/www
      - ./docker/nginx/conf.d/:/etc/nginx/conf.d/
    networks:
      - santrimental
    depends_on:
      - app

  # MySQL Database
  db:
    image: mysql:8.0
    container_name: santrimental_db
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: santrimental
      MYSQL_USER: santrimental
      MYSQL_PASSWORD: secret
      MYSQL_ROOT_PASSWORD: root
      SERVICE_TAGS: dev
      SERVICE_NAME: mysql
    volumes:
      - dbdata:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf
    networks:
      - santrimental

  # Redis Cache
  redis:
    image: redis:alpine
    container_name: santrimental_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redisdata:/data
    networks:
      - santrimental

  # Queue Worker
  queue:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: santrimental_queue
    restart: unless-stopped
    command: php artisan queue:work --verbose --tries=3 --timeout=90
    working_dir: /var/www
    volumes:
      - ./:/var/www
    networks:
      - santrimental
    depends_on:
      - db
      - redis
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_HOST=db
      - DB_DATABASE=santrimental
      - DB_USERNAME=santrimental
      - DB_PASSWORD=secret
      - REDIS_HOST=redis

  # Scheduler
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: santrimental_scheduler
    restart: unless-stopped
    command: php artisan schedule:work
    working_dir: /var/www
    volumes:
      - ./:/var/www
    networks:
      - santrimental
    depends_on:
      - db
      - redis
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_HOST=db
      - DB_DATABASE=santrimental
      - DB_USERNAME=santrimental
      - DB_PASSWORD=secret
      - REDIS_HOST=redis

  # PHPMyAdmin
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: santrimental_phpmyadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
      PMA_USER: santrimental
      PMA_PASSWORD: secret
    networks:
      - santrimental
    depends_on:
      - db

  # Redis Commander
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: santrimental_redis_commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    networks:
      - santrimental
    depends_on:
      - redis

  # Mailhog (for email testing)
  mailhog:
    image: mailhog/mailhog
    container_name: santrimental_mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"
      - "8025:8025"
    networks:
      - santrimental

networks:
  santrimental:
    driver: bridge

volumes:
  dbdata:
    driver: local
  redisdata:
    driver: local
