<?php

namespace App\Modules\Assessment\Providers;

use Illuminate\Support\ServiceProvider;
use App\Modules\Assessment\Models\AssessmentForm;
use App\Modules\Assessment\Models\AssessmentResponse;
use App\Modules\Assessment\Repositories\AssessmentRepository;
use App\Modules\Assessment\Repositories\AssessmentResponseRepository;
use App\Modules\Assessment\Services\AssessmentService;

class AssessmentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Bind repositories
        $this->app->bind(AssessmentRepository::class, function ($app) {
            return new AssessmentRepository(new AssessmentForm());
        });

        $this->app->bind(AssessmentResponseRepository::class, function ($app) {
            return new AssessmentResponseRepository(new AssessmentResponse());
        });

        // Bind services
        $this->app->bind(AssessmentService::class, function ($app) {
            return new AssessmentService(
                $app->make(AssessmentRepository::class),
                $app->make(AssessmentResponseRepository::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load module migrations
        $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');
        
        // Load module views if any
        // $this->loadViewsFrom(__DIR__ . '/../resources/views', 'assessment');
        
        // Publish module assets if any
        // $this->publishes([
        //     __DIR__ . '/../resources/assets' => public_path('vendor/assessment'),
        // ], 'assessment-assets');
    }
}
