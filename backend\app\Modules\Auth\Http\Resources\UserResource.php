<?php

namespace App\Modules\Auth\Http\Resources;

use App\Core\Http\Resources\BaseResource;
use Illuminate\Http\Request;

class UserResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'full_name' => $this->full_name,
            'email' => $this->email,
            'email_verified_at' => $this->email_verified_at?->toISOString(),
            'phone' => $this->phone,
            'student_id' => $this->student_id,
            'class' => $this->class,
            'grade' => $this->grade,
            'avatar' => $this->avatar ? asset('storage/' . $this->avatar) : null,
            'avatar_url' => $this->avatarUrl($this->avatar, $this->full_name),
            'is_active' => $this->is_active,
            'last_login_at' => $this->last_login_at?->toISOString(),
            'last_login_human' => $this->humanDate($this->last_login_at),
            
            // Role information
            'role' => $this->whenLoaded('role', function () {
                return [
                    'id' => $this->role->id,
                    'name' => $this->role->name,
                    'display_name' => $this->role->display_name,
                    'permissions' => $this->role->permissions ?? []
                ];
            }),
            
            // Role shortcuts
            'role_name' => $this->role?->name,
            'role_display_name' => $this->role?->display_name,
            'is_admin' => $this->isAdmin(),
            'is_guru' => $this->isGuru(),
            'is_orangtua' => $this->isOrangtua(),
            'is_siswa' => $this->isSiswa(),
            
            // Parent-child relationships
            'children' => $this->whenLoaded('children', function () {
                return $this->children->map(function ($child) {
                    return [
                        'id' => $child->id,
                        'full_name' => $child->full_name,
                        'student_id' => $child->student_id,
                        'class' => $child->class,
                        'grade' => $child->grade,
                        'avatar_url' => $this->avatarUrl($child->avatar, $child->full_name)
                    ];
                });
            }),
            
            'parents' => $this->whenLoaded('parents', function () {
                return $this->parents->map(function ($parent) {
                    return [
                        'id' => $parent->id,
                        'full_name' => $parent->full_name,
                        'email' => $parent->email,
                        'phone' => $parent->phone,
                        'avatar_url' => $this->avatarUrl($parent->avatar, $parent->full_name)
                    ];
                });
            }),
            
            // Teacher information
            'teacher' => $this->whenLoaded('teacher', function () {
                return $this->teacher ? [
                    'id' => $this->teacher->id,
                    'full_name' => $this->teacher->full_name,
                    'email' => $this->teacher->email,
                    'phone' => $this->teacher->phone,
                    'avatar_url' => $this->avatarUrl($this->teacher->avatar, $this->teacher->full_name)
                ] : null;
            }),
            
            // Students (for teachers)
            'students' => $this->whenLoaded('students', function () {
                return $this->students->map(function ($student) {
                    return [
                        'id' => $student->id,
                        'full_name' => $student->full_name,
                        'student_id' => $student->student_id,
                        'class' => $student->class,
                        'grade' => $student->grade,
                        'avatar_url' => $this->avatarUrl($student->avatar, $student->full_name)
                    ];
                });
            }),
            
            // Assessment statistics
            'assessment_stats' => $this->when(
                $request->has('include_stats'),
                function () {
                    return [
                        'total_assessments' => $this->assessmentResponses()->count(),
                        'completed_assessments' => $this->assessmentResponses()->where('status', 'completed')->count(),
                        'pending_assessments' => $this->assessmentResponses()->where('status', 'started')->count(),
                        'last_assessment_at' => $this->assessmentResponses()
                            ->latest('completed_at')
                            ->first()?->completed_at?->toISOString()
                    ];
                }
            ),
            
            // Profile completion
            'profile_completion' => $this->getProfileCompletion(),
            'profile_completion_percentage' => $this->getProfileCompletionPercentage(),
            
            // Security information
            'two_factor_enabled' => false, // TODO: Implement 2FA
            'email_verified' => !is_null($this->email_verified_at),
            'account_status' => $this->getAccountStatus(),
            
            // Preferences
            'preferences' => $this->preferences ?? [],
            'timezone' => $this->timezone ?? 'Asia/Jakarta',
            'locale' => $this->locale ?? 'id',
            
            // Timestamps
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            'created_at_human' => $this->humanDate($this->created_at),
            'updated_at_human' => $this->humanDate($this->updated_at),
        ];
    }

    /**
     * Get profile completion status
     */
    private function getProfileCompletion(): array
    {
        $required = ['first_name', 'last_name', 'email'];
        $optional = ['phone', 'avatar', 'student_id', 'class', 'grade'];
        
        $completed = [];
        $missing = [];
        
        foreach ($required as $field) {
            if ($this->{$field}) {
                $completed[] = $field;
            } else {
                $missing[] = $field;
            }
        }
        
        foreach ($optional as $field) {
            if ($this->{$field}) {
                $completed[] = $field;
            }
        }
        
        return [
            'completed' => $completed,
            'missing_required' => array_intersect($missing, $required),
            'total_fields' => count($required) + count($optional),
            'completed_fields' => count($completed)
        ];
    }

    /**
     * Get profile completion percentage
     */
    private function getProfileCompletionPercentage(): int
    {
        $completion = $this->getProfileCompletion();
        return round(($completion['completed_fields'] / $completion['total_fields']) * 100);
    }

    /**
     * Get account status
     */
    private function getAccountStatus(): string
    {
        if (!$this->is_active) {
            return 'inactive';
        }
        
        if (is_null($this->email_verified_at)) {
            return 'unverified';
        }
        
        return 'active';
    }
}
