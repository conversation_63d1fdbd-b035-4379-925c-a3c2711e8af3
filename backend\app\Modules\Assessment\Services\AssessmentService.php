<?php

namespace App\Modules\Assessment\Services;

use App\Core\Services\BaseService;
use App\Modules\Assessment\Models\AssessmentForm;
use App\Modules\Assessment\Models\AssessmentResponse;
use App\Modules\Assessment\Repositories\AssessmentRepository;
use App\Modules\Assessment\Repositories\AssessmentResponseRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;

class AssessmentService extends BaseService
{
    public function __construct(
        private AssessmentRepository $assessmentRepository,
        private AssessmentResponseRepository $responseRepository
    ) {}

    /**
     * Get all available assessment forms
     */
    public function getAvailableForms(): Collection
    {
        return Cache::remember(
            'assessment_forms_available',
            now()->addHours(1),
            fn() => $this->assessmentRepository->getActive()
        );
    }

    /**
     * Get assessment form by code
     */
    public function getFormByCode(string $code): ?AssessmentForm
    {
        return Cache::remember(
            "assessment_form_{$code}",
            now()->addHours(1),
            fn() => $this->assessmentRepository->findByCode($code)
        );
    }

    /**
     * Get forms by category
     */
    public function getFormsByCategory(string $category): Collection
    {
        return Cache::remember(
            "assessment_forms_category_{$category}",
            now()->addHours(1),
            fn() => $this->assessmentRepository->getByCategory($category)
        );
    }

    /**
     * Get available forms for user
     */
    public function getAvailableFormsForUser(int $userId): Collection
    {
        return $this->assessmentRepository->getAvailableForUser($userId);
    }

    /**
     * Start assessment for user
     */
    public function startAssessment(int $userId, string $formCode): AssessmentResponse
    {
        return $this->executeTransaction(function () use ($userId, $formCode) {
            $form = $this->getFormByCode($formCode);
            
            if (!$form) {
                throw new \Exception("Assessment form '{$formCode}' not found");
            }

            if (!$form->is_active) {
                throw new \Exception("Assessment form '{$formCode}' is not active");
            }

            // Check if user has an incomplete response
            $existingResponse = $this->responseRepository->getUserLatestResponse($userId, $form->id);
            
            if ($existingResponse && $existingResponse->isInProgress()) {
                return $existingResponse;
            }

            // Create new response
            $response = $this->responseRepository->create([
                'user_id' => $userId,
                'form_id' => $form->id,
                'status' => AssessmentResponse::STATUS_STARTED,
                'started_at' => now(),
            ]);

            $this->logActivity($response, 'assessment_started');

            return $response;
        }, 'start assessment');
    }

    /**
     * Submit assessment answers
     */
    public function submitAssessment(string $responseId, array $answers): AssessmentResponse
    {
        return $this->executeTransaction(function () use ($responseId, $answers) {
            $response = $this->responseRepository->findByIdOrFail($responseId);
            
            if ($response->isCompleted()) {
                throw new \Exception('Assessment already completed');
            }

            $form = $response->form;
            
            // Validate answers
            $this->validateAnswers($form, $answers);

            // Calculate score
            $scoreData = $form->calculateScore($answers);
            
            // Get interpretation
            $interpretation = $form->getInterpretation($scoreData);

            // Mark as completed
            $response->markAsCompleted($answers, $scoreData, $interpretation);

            $this->logActivity($response, 'assessment_completed', [
                'score' => $scoreData['total_score'],
                'interpretation' => $interpretation['level']
            ]);

            return $response->fresh();
        }, 'submit assessment');
    }

    /**
     * Save assessment progress
     */
    public function saveProgress(string $responseId, array $answers): AssessmentResponse
    {
        return $this->executeTransaction(function () use ($responseId, $answers) {
            $response = $this->responseRepository->findByIdOrFail($responseId);
            
            if ($response->isCompleted()) {
                throw new \Exception('Assessment already completed');
            }

            $response->update([
                'answers' => $answers,
                'status' => AssessmentResponse::STATUS_IN_PROGRESS,
            ]);

            $this->logActivity($response, 'assessment_progress_saved');

            return $response->fresh();
        }, 'save assessment progress');
    }

    /**
     * Get user's assessment history
     */
    public function getUserHistory(int $userId): Collection
    {
        return $this->responseRepository->getUserCompletedResponses($userId);
    }

    /**
     * Get user's assessment statistics
     */
    public function getUserStatistics(int $userId): array
    {
        return $this->responseRepository->getUserStatistics($userId);
    }

    /**
     * Get assessment form statistics
     */
    public function getFormStatistics(string $formId): array
    {
        return $this->responseRepository->getFormStatistics($formId);
    }

    /**
     * Get overall assessment statistics
     */
    public function getOverallStatistics(): array
    {
        return Cache::remember(
            'assessment_overall_statistics',
            now()->addMinutes(30),
            fn() => $this->assessmentRepository->getStatistics()
        );
    }

    /**
     * Create new assessment form
     */
    public function createForm(array $data): AssessmentForm
    {
        return $this->executeTransaction(function () use ($data) {
            $this->validateRequired($data, ['code', 'name', 'questions', 'scoring_rules', 'interpretation_rules']);
            
            $form = $this->assessmentRepository->create($data);

            // Clear cache
            Cache::forget('assessment_forms_available');
            Cache::forget("assessment_form_{$form->code}");

            $this->logActivity($form, 'form_created');

            return $form;
        }, 'create assessment form');
    }

    /**
     * Update assessment form
     */
    public function updateForm(AssessmentForm $form, array $data): AssessmentForm
    {
        return $this->executeTransaction(function () use ($form, $data) {
            $updatedForm = $this->assessmentRepository->update($form, $data);

            // Clear cache
            Cache::forget('assessment_forms_available');
            Cache::forget("assessment_form_{$form->code}");
            Cache::forget("assessment_forms_category_{$form->category}");

            $this->logActivity($updatedForm, 'form_updated');

            return $updatedForm;
        }, 'update assessment form');
    }

    /**
     * Delete assessment form
     */
    public function deleteForm(AssessmentForm $form): bool
    {
        return $this->executeTransaction(function () use ($form) {
            $result = $this->assessmentRepository->delete($form);

            // Clear cache
            Cache::forget('assessment_forms_available');
            Cache::forget("assessment_form_{$form->code}");
            Cache::forget("assessment_forms_category_{$form->category}");

            $this->logActivity($form, 'form_deleted');

            return $result;
        }, 'delete assessment form');
    }

    /**
     * Get paginated forms with filters
     */
    public function getPaginatedForms(array $filters): LengthAwarePaginator
    {
        return $this->assessmentRepository->searchWithFilters($filters);
    }

    /**
     * Get paginated responses with filters
     */
    public function getPaginatedResponses(array $filters): LengthAwarePaginator
    {
        return $this->responseRepository->getWithFilters($filters);
    }

    /**
     * Validate assessment answers
     */
    private function validateAnswers(AssessmentForm $form, array $answers): void
    {
        $questions = $form->questions;
        
        foreach ($questions as $index => $question) {
            $questionKey = "question_{$index}";
            $answer = $answers[$questionKey] ?? null;

            // Check if required question is answered
            if (($question['required'] ?? true) && $answer === null) {
                throw new \Exception("Question " . ($index + 1) . " is required");
            }

            // Validate answer format based on question type
            if ($answer !== null) {
                $this->validateAnswerFormat($question, $answer);
            }
        }
    }

    /**
     * Validate answer format
     */
    private function validateAnswerFormat(array $question, $answer): void
    {
        $type = $question['type'];

        switch ($type) {
            case 'likert':
                if (!is_numeric($answer) || $answer < 0 || $answer > 4) {
                    throw new \Exception('Invalid likert scale answer');
                }
                break;

            case 'yes_no':
                if (!in_array($answer, ['yes', 'no'])) {
                    throw new \Exception('Invalid yes/no answer');
                }
                break;

            case 'multiple_choice':
                $options = array_keys($question['options'] ?? []);
                if (!in_array($answer, $options)) {
                    throw new \Exception('Invalid multiple choice answer');
                }
                break;
        }
    }
}
