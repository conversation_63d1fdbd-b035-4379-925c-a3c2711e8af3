<?php

namespace App\Modules\Assessment\Http\Requests;

use App\Core\Http\Requests\BaseRequest;

class StartAssessmentRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'form_code' => 'required|string|exists:assessment_forms,code',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'form_code.required' => 'Assessment form code is required',
            'form_code.exists' => 'Invalid assessment form code',
        ]);
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return array_merge(parent::attributes(), [
            'form_code' => 'assessment form code',
        ]);
    }
}
