<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AssessmentAnswer extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'assessment_id',
        'question_number',
        'answer',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'question_number' => 'integer',
        'answer' => 'boolean',
    ];

    /**
     * Get the assessment that owns the answer.
     */
    public function assessment(): BelongsTo
    {
        return $this->belongsTo(Assessment::class);
    }

    /**
     * Get the question text for this answer.
     *
     * @return string
     */
    public function getQuestionText(): string
    {
        return self::getQuestions()[$this->question_number - 1];
    }

    /**
     * Get all SRQ-20 questions.
     *
     * @return array
     */
    public static function getQuestions(): array
    {
        return [
            "Apakah Anda sering merasa sakit kepala?",
            "Apakah nafsu makan Anda buruk?",
            "Apakah Anda tidur tidak nyenyak?",
            "Apakah Anda mudah merasa takut?",
            "Apakah Anda merasa cemas, tegang, atau khawatir?",
            "Apakah tangan Anda gemetar?",
            "Apakah pencernaan Anda terganggu atau buruk?",
            "Apakah Anda sulit berpikir jernih?",
            "Apakah Anda merasa tidak bahagia?",
            "Apakah Anda lebih sering menangis?",
            "Apakah Anda merasa sulit untuk menikmati kegiatan sehari-hari?",
            "Apakah Anda mengalami kesulitan dalam mengambil keputusan?",
            "Apakah pekerjaan sehari-hari Anda terganggu?",
            "Apakah Anda tidak mampu melakukan hal-hal yang bermanfaat dalam hidup?",
            "Apakah Anda kehilangan minat pada berbagai hal?",
            "Apakah Anda merasa tidak berharga?",
            "Apakah Anda mempunyai pikiran untuk mengakhiri hidup Anda?",
            "Apakah Anda merasa lelah sepanjang waktu?",
            "Apakah Anda merasa tidak enak di perut?",
            "Apakah Anda mudah lelah?"
        ];
    }

    /**
     * Get answers summary for an assessment.
     *
     * @param int $assessmentId
     * @return array
     */
    public static function getAnswersSummary(int $assessmentId): array
    {
        $answers = self::where('assessment_id', $assessmentId)
            ->orderBy('question_number')
            ->get();

        $questions = self::getQuestions();
        $summary = [];

        foreach ($answers as $answer) {
            $summary[] = [
                'question' => $questions[$answer->question_number - 1],
                'answer' => $answer->answer,
                'question_number' => $answer->question_number
            ];
        }

        return $summary;
    }

    /**
     * Validate answers for completeness.
     *
     * @param array $answers
     * @return bool
     */
    public static function validateAnswers(array $answers): bool
    {
        // Check if we have exactly 20 answers
        if (count($answers) !== 20) {
            return false;
        }

        // Check if all question numbers are present and valid
        $questionNumbers = array_column($answers, 'question_number');
        sort($questionNumbers);
        
        return $questionNumbers === range(1, 20);
    }
}
