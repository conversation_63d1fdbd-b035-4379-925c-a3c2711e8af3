<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Modules\Assessment\Models\AssessmentForm;
use Illuminate\Support\Str;

class AssessmentFormSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $forms = [
            $this->getSRQ20Form(),
            $this->getMHKQForm(),
            $this->getPDDForm(),
            $this->getGSEForm(),
            $this->getMSCSForm(),
            $this->getPHQ9Form(),
            $this->getDASS42Form(),
        ];

        foreach ($forms as $form) {
            AssessmentForm::updateOrCreate(
                ['code' => $form['code']],
                $form
            );
        }
    }

    /**
     * SRQ-20 (Self Reporting Questionnaire)
     */
    private function getSRQ20Form(): array
    {
        return [
            'id' => Str::uuid(),
            'code' => 'SRQ20',
            'name' => 'Self Reporting Questionnaire (SRQ-20)',
            'description' => 'Kuesioner untuk mendeteksi gangguan mental umum',
            'category' => 'mental_health',
            'time_limit' => 10,
            'questions' => [
                [
                    'text' => 'Apakah Anda sering mengalami sakit kepala?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah nafsu makan Anda berkurang?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah Anda sulit tidur?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah Anda mudah takut?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah tangan Anda gemetar?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah Anda merasa cemas, tegang atau khawatir?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah pencernaan Anda terganggu?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah Anda sulit berpikir jernih?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah Anda merasa tidak bahagia?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah Anda menangis lebih sering dari biasanya?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah Anda sulit menikmati aktivitas sehari-hari?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah Anda sulit mengambil keputusan?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah pekerjaan sehari-hari Anda terganggu?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah Anda tidak mampu berperan dalam kehidupan?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah Anda kehilangan minat terhadap berbagai hal?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah Anda merasa tidak berharga?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah Anda pernah berpikir untuk mengakhiri hidup?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah Anda merasa lelah sepanjang waktu?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah Anda merasa tidak nyaman di perut?',
                    'type' => 'yes_no',
                    'required' => true
                ],
                [
                    'text' => 'Apakah Anda mudah lelah?',
                    'type' => 'yes_no',
                    'required' => true
                ]
            ],
            'scoring_rules' => [
                'yes_no' => [
                    'yes' => 1,
                    'no' => 0
                ]
            ],
            'interpretation_rules' => [
                [
                    'score_range' => ['min' => 0, 'max' => 7],
                    'level' => 'normal',
                    'label' => 'Normal',
                    'description' => 'Tidak ada indikasi gangguan mental',
                    'color' => 'green',
                    'recommendations' => [
                        'Pertahankan gaya hidup sehat',
                        'Lakukan aktivitas yang menyenangkan',
                        'Jaga hubungan sosial yang baik'
                    ]
                ],
                [
                    'score_range' => ['min' => 8, 'max' => 20],
                    'level' => 'probable_disorder',
                    'label' => 'Kemungkinan Gangguan Mental',
                    'description' => 'Ada indikasi kemungkinan gangguan mental yang perlu perhatian',
                    'color' => 'red',
                    'recommendations' => [
                        'Konsultasi dengan tenaga kesehatan mental',
                        'Lakukan teknik relaksasi',
                        'Cari dukungan dari keluarga dan teman',
                        'Pertimbangkan untuk mendapatkan bantuan profesional'
                    ]
                ]
            ],
            'is_active' => true
        ];
    }

    /**
     * MHKQ (Mental Health Knowledge Questionnaire)
     */
    private function getMHKQForm(): array
    {
        return [
            'id' => Str::uuid(),
            'code' => 'MHKQ',
            'name' => 'Mental Health Knowledge Questionnaire (MHKQ)',
            'description' => 'Kuesioner untuk mengukur pengetahuan tentang kesehatan mental',
            'category' => 'knowledge',
            'time_limit' => 15,
            'questions' => [
                [
                    'text' => 'Gangguan mental dapat disembuhkan dengan pengobatan yang tepat',
                    'type' => 'multiple_choice',
                    'options' => [
                        'strongly_agree' => 'Sangat Setuju',
                        'agree' => 'Setuju',
                        'neutral' => 'Netral',
                        'disagree' => 'Tidak Setuju',
                        'strongly_disagree' => 'Sangat Tidak Setuju'
                    ],
                    'required' => true
                ],
                [
                    'text' => 'Orang dengan gangguan mental berbahaya bagi masyarakat',
                    'type' => 'multiple_choice',
                    'options' => [
                        'strongly_agree' => 'Sangat Setuju',
                        'agree' => 'Setuju',
                        'neutral' => 'Netral',
                        'disagree' => 'Tidak Setuju',
                        'strongly_disagree' => 'Sangat Tidak Setuju'
                    ],
                    'required' => true
                ],
                [
                    'text' => 'Gangguan mental disebabkan oleh kelemahan karakter',
                    'type' => 'multiple_choice',
                    'options' => [
                        'strongly_agree' => 'Sangat Setuju',
                        'agree' => 'Setuju',
                        'neutral' => 'Netral',
                        'disagree' => 'Tidak Setuju',
                        'strongly_disagree' => 'Sangat Tidak Setuju'
                    ],
                    'required' => true
                ],
                [
                    'text' => 'Terapi psikologi efektif untuk mengatasi gangguan mental',
                    'type' => 'multiple_choice',
                    'options' => [
                        'strongly_agree' => 'Sangat Setuju',
                        'agree' => 'Setuju',
                        'neutral' => 'Netral',
                        'disagree' => 'Tidak Setuju',
                        'strongly_disagree' => 'Sangat Tidak Setuju'
                    ],
                    'required' => true
                ],
                [
                    'text' => 'Orang dengan gangguan mental tidak dapat bekerja secara produktif',
                    'type' => 'multiple_choice',
                    'options' => [
                        'strongly_agree' => 'Sangat Setuju',
                        'agree' => 'Setuju',
                        'neutral' => 'Netral',
                        'disagree' => 'Tidak Setuju',
                        'strongly_disagree' => 'Sangat Tidak Setuju'
                    ],
                    'required' => true
                ]
            ],
            'scoring_rules' => [
                'multiple_choice' => [
                    'strongly_agree' => 5,
                    'agree' => 4,
                    'neutral' => 3,
                    'disagree' => 2,
                    'strongly_disagree' => 1
                ]
            ],
            'interpretation_rules' => [
                [
                    'score_range' => ['min' => 5, 'max' => 15],
                    'level' => 'low',
                    'label' => 'Pengetahuan Rendah',
                    'description' => 'Pengetahuan tentang kesehatan mental masih terbatas',
                    'color' => 'red',
                    'recommendations' => [
                        'Pelajari lebih lanjut tentang kesehatan mental',
                        'Ikuti edukasi kesehatan mental',
                        'Konsultasi dengan ahli kesehatan mental'
                    ]
                ],
                [
                    'score_range' => ['min' => 16, 'max' => 20],
                    'level' => 'moderate',
                    'label' => 'Pengetahuan Sedang',
                    'description' => 'Pengetahuan tentang kesehatan mental cukup baik',
                    'color' => 'yellow',
                    'recommendations' => [
                        'Tingkatkan pemahaman tentang kesehatan mental',
                        'Bagikan pengetahuan kepada orang lain'
                    ]
                ],
                [
                    'score_range' => ['min' => 21, 'max' => 25],
                    'level' => 'high',
                    'label' => 'Pengetahuan Tinggi',
                    'description' => 'Pengetahuan tentang kesehatan mental sangat baik',
                    'color' => 'green',
                    'recommendations' => [
                        'Pertahankan pengetahuan yang baik',
                        'Bantu edukasi orang lain tentang kesehatan mental'
                    ]
                ]
            ],
            'is_active' => true
        ];
    }

    /**
     * PDD (Perceived Devaluation Discrimination)
     */
    private function getPDDForm(): array
    {
        return [
            'id' => Str::uuid(),
            'code' => 'PDD',
            'name' => 'Perceived Devaluation Discrimination (PDD)',
            'description' => 'Skala untuk mengukur persepsi diskriminasi dan devaluasi',
            'category' => 'discrimination',
            'time_limit' => 10,
            'questions' => [
                [
                    'text' => 'Kebanyakan orang akan menerima mantan pasien rumah sakit jiwa sebagai guru anak-anak mereka',
                    'type' => 'multiple_choice',
                    'options' => [
                        'strongly_agree' => 'Sangat Setuju',
                        'agree' => 'Setuju',
                        'disagree' => 'Tidak Setuju',
                        'strongly_disagree' => 'Sangat Tidak Setuju'
                    ],
                    'required' => true
                ],
                [
                    'text' => 'Kebanyakan orang percaya bahwa seseorang yang pernah dirawat di rumah sakit jiwa sama tidak dapat diandalkannya dengan orang lain',
                    'type' => 'multiple_choice',
                    'options' => [
                        'strongly_agree' => 'Sangat Setuju',
                        'agree' => 'Setuju',
                        'disagree' => 'Tidak Setuju',
                        'strongly_disagree' => 'Sangat Tidak Setuju'
                    ],
                    'required' => true
                ],
                [
                    'text' => 'Kebanyakan majikan akan mempekerjakan mantan pasien rumah sakit jiwa jika dia memenuhi syarat untuk pekerjaan tersebut',
                    'type' => 'multiple_choice',
                    'options' => [
                        'strongly_agree' => 'Sangat Setuju',
                        'agree' => 'Setuju',
                        'disagree' => 'Tidak Setuju',
                        'strongly_disagree' => 'Sangat Tidak Setuju'
                    ],
                    'required' => true
                ]
            ],
            'scoring_rules' => [
                'multiple_choice' => [
                    'strongly_agree' => 1,
                    'agree' => 2,
                    'disagree' => 3,
                    'strongly_disagree' => 4
                ]
            ],
            'interpretation_rules' => [
                [
                    'score_range' => ['min' => 3, 'max' => 6],
                    'level' => 'high_discrimination',
                    'label' => 'Persepsi Diskriminasi Tinggi',
                    'description' => 'Merasakan tingkat diskriminasi yang tinggi',
                    'color' => 'red',
                    'recommendations' => [
                        'Cari dukungan sosial',
                        'Konsultasi dengan konselor',
                        'Ikuti program anti-stigma'
                    ]
                ],
                [
                    'score_range' => ['min' => 7, 'max' => 12],
                    'level' => 'low_discrimination',
                    'label' => 'Persepsi Diskriminasi Rendah',
                    'description' => 'Merasakan tingkat diskriminasi yang rendah',
                    'color' => 'green',
                    'recommendations' => [
                        'Pertahankan pandangan positif',
                        'Bantu mengurangi stigma di masyarakat'
                    ]
                ]
            ],
            'is_active' => true
        ];
    }

    /**
     * GSE (General Self-Efficacy)
     */
    private function getGSEForm(): array
    {
        return [
            'id' => Str::uuid(),
            'code' => 'GSE',
            'name' => 'General Self-Efficacy Scale (GSE)',
            'description' => 'Skala untuk mengukur efikasi diri umum',
            'category' => 'self_efficacy',
            'time_limit' => 10,
            'questions' => [
                [
                    'text' => 'Saya dapat menyelesaikan masalah sulit jika saya berusaha keras',
                    'type' => 'likert',
                    'scale' => ['Tidak Benar', 'Hampir Tidak Benar', 'Agak Benar', 'Benar'],
                    'required' => true
                ],
                [
                    'text' => 'Jika seseorang menentang saya, saya dapat menemukan cara untuk mendapatkan apa yang saya inginkan',
                    'type' => 'likert',
                    'scale' => ['Tidak Benar', 'Hampir Tidak Benar', 'Agak Benar', 'Benar'],
                    'required' => true
                ],
                [
                    'text' => 'Mudah bagi saya untuk berpegang pada tujuan dan mencapai target saya',
                    'type' => 'likert',
                    'scale' => ['Tidak Benar', 'Hampir Tidak Benar', 'Agak Benar', 'Benar'],
                    'required' => true
                ],
                [
                    'text' => 'Saya yakin bahwa saya dapat menangani kejadian tak terduga secara efektif',
                    'type' => 'likert',
                    'scale' => ['Tidak Benar', 'Hampir Tidak Benar', 'Agak Benar', 'Benar'],
                    'required' => true
                ],
                [
                    'text' => 'Berkat kecerdikan saya, saya tahu bagaimana menangani situasi yang tidak terduga',
                    'type' => 'likert',
                    'scale' => ['Tidak Benar', 'Hampir Tidak Benar', 'Agak Benar', 'Benar'],
                    'required' => true
                ]
            ],
            'scoring_rules' => [
                'likert' => [
                    'min' => 1,
                    'max' => 4
                ]
            ],
            'interpretation_rules' => [
                [
                    'score_range' => ['min' => 5, 'max' => 10],
                    'level' => 'low',
                    'label' => 'Efikasi Diri Rendah',
                    'description' => 'Tingkat kepercayaan diri dalam mengatasi masalah masih rendah',
                    'color' => 'red',
                    'recommendations' => [
                        'Latih kemampuan problem solving',
                        'Mulai dengan tantangan kecil',
                        'Cari dukungan dari orang lain'
                    ]
                ],
                [
                    'score_range' => ['min' => 11, 'max' => 15],
                    'level' => 'moderate',
                    'label' => 'Efikasi Diri Sedang',
                    'description' => 'Tingkat kepercayaan diri dalam mengatasi masalah cukup baik',
                    'color' => 'yellow',
                    'recommendations' => [
                        'Tingkatkan kepercayaan diri',
                        'Tantang diri dengan tugas yang lebih sulit'
                    ]
                ],
                [
                    'score_range' => ['min' => 16, 'max' => 20],
                    'level' => 'high',
                    'label' => 'Efikasi Diri Tinggi',
                    'description' => 'Tingkat kepercayaan diri dalam mengatasi masalah sangat baik',
                    'color' => 'green',
                    'recommendations' => [
                        'Pertahankan kepercayaan diri yang baik',
                        'Bantu orang lain mengembangkan kepercayaan diri'
                    ]
                ]
            ],
            'is_active' => true
        ];
    }

    /**
     * MSCS (Mindful Self-Care Scale)
     */
    private function getMSCSForm(): array
    {
        return [
            'id' => Str::uuid(),
            'code' => 'MSCS',
            'name' => 'Mindful Self-Care Scale (MSCS)',
            'description' => 'Skala untuk mengukur perawatan diri yang mindful',
            'category' => 'self_care',
            'time_limit' => 15,
            'questions' => [
                [
                    'text' => 'Saya makan makanan yang sehat dan bergizi',
                    'type' => 'likert',
                    'scale' => ['Tidak Pernah', 'Jarang', 'Kadang-kadang', 'Sering', 'Selalu'],
                    'required' => true
                ],
                [
                    'text' => 'Saya berolahraga secara teratur',
                    'type' => 'likert',
                    'scale' => ['Tidak Pernah', 'Jarang', 'Kadang-kadang', 'Sering', 'Selalu'],
                    'required' => true
                ],
                [
                    'text' => 'Saya mendapatkan tidur yang cukup',
                    'type' => 'likert',
                    'scale' => ['Tidak Pernah', 'Jarang', 'Kadang-kadang', 'Sering', 'Selalu'],
                    'required' => true
                ],
                [
                    'text' => 'Saya meluangkan waktu untuk relaksasi',
                    'type' => 'likert',
                    'scale' => ['Tidak Pernah', 'Jarang', 'Kadang-kadang', 'Sering', 'Selalu'],
                    'required' => true
                ],
                [
                    'text' => 'Saya melakukan aktivitas yang saya nikmati',
                    'type' => 'likert',
                    'scale' => ['Tidak Pernah', 'Jarang', 'Kadang-kadang', 'Sering', 'Selalu'],
                    'required' => true
                ]
            ],
            'scoring_rules' => [
                'likert' => [
                    'min' => 1,
                    'max' => 5
                ]
            ],
            'interpretation_rules' => [
                [
                    'score_range' => ['min' => 5, 'max' => 12],
                    'level' => 'poor',
                    'label' => 'Perawatan Diri Kurang',
                    'description' => 'Tingkat perawatan diri masih kurang optimal',
                    'color' => 'red',
                    'recommendations' => [
                        'Buat jadwal perawatan diri harian',
                        'Mulai dengan kebiasaan kecil',
                        'Prioritaskan kesehatan fisik dan mental'
                    ]
                ],
                [
                    'score_range' => ['min' => 13, 'max' => 19],
                    'level' => 'moderate',
                    'label' => 'Perawatan Diri Sedang',
                    'description' => 'Tingkat perawatan diri cukup baik',
                    'color' => 'yellow',
                    'recommendations' => [
                        'Tingkatkan konsistensi perawatan diri',
                        'Eksplorasi aktivitas perawatan diri baru'
                    ]
                ],
                [
                    'score_range' => ['min' => 20, 'max' => 25],
                    'level' => 'excellent',
                    'label' => 'Perawatan Diri Sangat Baik',
                    'description' => 'Tingkat perawatan diri sangat optimal',
                    'color' => 'green',
                    'recommendations' => [
                        'Pertahankan kebiasaan perawatan diri yang baik',
                        'Bagikan tips perawatan diri kepada orang lain'
                    ]
                ]
            ],
            'is_active' => true
        ];
    }

    /**
     * PHQ-9 (Patient Health Questionnaire-9)
     */
    private function getPHQ9Form(): array
    {
        return [
            'id' => Str::uuid(),
            'code' => 'PHQ9',
            'name' => 'Patient Health Questionnaire-9 (PHQ-9)',
            'description' => 'Kuesioner untuk mendeteksi dan mengukur tingkat depresi',
            'category' => 'mental_health',
            'time_limit' => 10,
            'questions' => [
                [
                    'text' => 'Sedikit minat atau kesenangan dalam melakukan sesuatu',
                    'type' => 'likert',
                    'scale' => ['Tidak sama sekali', 'Beberapa hari', 'Lebih dari setengah hari', 'Hampir setiap hari'],
                    'required' => true
                ],
                [
                    'text' => 'Merasa sedih, tertekan, atau putus asa',
                    'type' => 'likert',
                    'scale' => ['Tidak sama sekali', 'Beberapa hari', 'Lebih dari setengah hari', 'Hampir setiap hari'],
                    'required' => true
                ],
                [
                    'text' => 'Kesulitan tidur atau tidur terlalu banyak',
                    'type' => 'likert',
                    'scale' => ['Tidak sama sekali', 'Beberapa hari', 'Lebih dari setengah hari', 'Hampir setiap hari'],
                    'required' => true
                ],
                [
                    'text' => 'Merasa lelah atau kurang energi',
                    'type' => 'likert',
                    'scale' => ['Tidak sama sekali', 'Beberapa hari', 'Lebih dari setengah hari', 'Hampir setiap hari'],
                    'required' => true
                ],
                [
                    'text' => 'Nafsu makan buruk atau makan berlebihan',
                    'type' => 'likert',
                    'scale' => ['Tidak sama sekali', 'Beberapa hari', 'Lebih dari setengah hari', 'Hampir setiap hari'],
                    'required' => true
                ]
            ],
            'scoring_rules' => [
                'likert' => [
                    'min' => 0,
                    'max' => 3
                ]
            ],
            'interpretation_rules' => [
                [
                    'score_range' => ['min' => 0, 'max' => 4],
                    'level' => 'minimal',
                    'label' => 'Depresi Minimal',
                    'description' => 'Tidak ada atau minimal gejala depresi',
                    'color' => 'green',
                    'recommendations' => [
                        'Pertahankan kesehatan mental yang baik',
                        'Lakukan aktivitas yang menyenangkan'
                    ]
                ],
                [
                    'score_range' => ['min' => 5, 'max' => 9],
                    'level' => 'mild',
                    'label' => 'Depresi Ringan',
                    'description' => 'Gejala depresi ringan',
                    'color' => 'yellow',
                    'recommendations' => [
                        'Monitor gejala secara berkala',
                        'Lakukan teknik relaksasi',
                        'Jaga pola hidup sehat'
                    ]
                ],
                [
                    'score_range' => ['min' => 10, 'max' => 15],
                    'level' => 'severe',
                    'label' => 'Depresi Berat',
                    'description' => 'Gejala depresi yang memerlukan perhatian serius',
                    'color' => 'red',
                    'recommendations' => [
                        'Segera konsultasi dengan tenaga kesehatan mental',
                        'Pertimbangkan terapi profesional',
                        'Cari dukungan dari keluarga dan teman'
                    ]
                ]
            ],
            'is_active' => true
        ];
    }

    /**
     * DASS-42 (Depression, Anxiety, Stress Scale)
     */
    private function getDASS42Form(): array
    {
        return [
            'id' => Str::uuid(),
            'code' => 'DASS42',
            'name' => 'Depression, Anxiety, Stress Scale (DASS-42)',
            'description' => 'Skala untuk mengukur tingkat depresi, kecemasan, dan stres',
            'category' => 'mental_health',
            'time_limit' => 20,
            'questions' => [
                [
                    'text' => 'Saya merasa sulit untuk tenang',
                    'type' => 'likert',
                    'scale' => ['Tidak pernah', 'Kadang-kadang', 'Sering', 'Hampir selalu'],
                    'subscale' => 'stress',
                    'required' => true
                ],
                [
                    'text' => 'Saya merasa mulut saya kering',
                    'type' => 'likert',
                    'scale' => ['Tidak pernah', 'Kadang-kadang', 'Sering', 'Hampir selalu'],
                    'subscale' => 'anxiety',
                    'required' => true
                ],
                [
                    'text' => 'Saya tidak dapat merasakan perasaan positif sama sekali',
                    'type' => 'likert',
                    'scale' => ['Tidak pernah', 'Kadang-kadang', 'Sering', 'Hampir selalu'],
                    'subscale' => 'depression',
                    'required' => true
                ],
                [
                    'text' => 'Saya mengalami kesulitan bernapas',
                    'type' => 'likert',
                    'scale' => ['Tidak pernah', 'Kadang-kadang', 'Sering', 'Hampir selalu'],
                    'subscale' => 'anxiety',
                    'required' => true
                ],
                [
                    'text' => 'Saya merasa sulit untuk memulai melakukan sesuatu',
                    'type' => 'likert',
                    'scale' => ['Tidak pernah', 'Kadang-kadang', 'Sering', 'Hampir selalu'],
                    'subscale' => 'depression',
                    'required' => true
                ]
            ],
            'scoring_rules' => [
                'likert' => [
                    'min' => 0,
                    'max' => 3
                ]
            ],
            'interpretation_rules' => [
                [
                    'score_range' => ['min' => 0, 'max' => 7],
                    'level' => 'normal',
                    'label' => 'Normal',
                    'description' => 'Tingkat depresi, kecemasan, dan stres dalam batas normal',
                    'color' => 'green',
                    'recommendations' => [
                        'Pertahankan kesehatan mental yang baik',
                        'Lakukan aktivitas yang menyenangkan'
                    ]
                ],
                [
                    'score_range' => ['min' => 8, 'max' => 15],
                    'level' => 'severe',
                    'label' => 'Berat',
                    'description' => 'Tingkat depresi, kecemasan, dan stres yang tinggi',
                    'color' => 'red',
                    'recommendations' => [
                        'Segera konsultasi dengan tenaga kesehatan mental',
                        'Lakukan teknik manajemen stres',
                        'Cari dukungan profesional'
                    ]
                ]
            ],
            'is_active' => true
        ];
    }
}
