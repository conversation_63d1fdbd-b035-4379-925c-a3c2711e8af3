name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: santrimental_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, dom, fileinfo, mysql, redis
        coverage: xdebug

    - name: Cache Composer packages
      id: composer-cache
      uses: actions/cache@v3
      with:
        path: vendor
        key: ${{ runner.os }}-php-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-php-

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress

    - name: Copy .env
      run: php -r "file_exists('.env') || copy('.env.example', '.env');"

    - name: Generate key
      run: php artisan key:generate

    - name: Directory Permissions
      run: chmod -R 777 storage bootstrap/cache

    - name: Create Database
      run: |
        mysql --host 127.0.0.1 --port 3306 -uroot -proot -e "CREATE DATABASE IF NOT EXISTS santrimental_test;"

    - name: Run Migrations
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: santrimental_test
        DB_USERNAME: root
        DB_PASSWORD: root
        REDIS_HOST: 127.0.0.1
        REDIS_PORT: 6379
      run: php artisan migrate --force

    - name: Seed Database
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: santrimental_test
        DB_USERNAME: root
        DB_PASSWORD: root
        REDIS_HOST: 127.0.0.1
        REDIS_PORT: 6379
      run: php artisan db:seed --force

    - name: Run PHPUnit Tests
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: santrimental_test
        DB_USERNAME: root
        DB_PASSWORD: root
        REDIS_HOST: 127.0.0.1
        REDIS_PORT: 6379
      run: php artisan test --coverage --min=80

    - name: Run PHPStan
      run: ./vendor/bin/phpstan analyse --memory-limit=2G

    - name: Run Laravel Pint
      run: ./vendor/bin/pint --test

  security:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress

    - name: Security Audit
      run: composer audit

  deploy:
    needs: [test, security]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress --no-dev --optimize-autoloader

    - name: Create deployment artifact
      env:
        GITHUB_SHA: ${{ github.sha }}
      run: tar -czf "${GITHUB_SHA}".tar.gz --exclude=*.git --exclude=node_modules *

    - name: Store artifact for distribution
      uses: actions/upload-artifact@v3
      with:
        name: app-build
        path: ${{ github.sha }}.tar.gz

    # Add your deployment steps here
    # - name: Deploy to production
    #   run: |
    #     # Your deployment commands
