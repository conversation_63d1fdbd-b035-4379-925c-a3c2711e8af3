<?php

namespace App\Modules\Assessment\Http\Requests;

use App\Core\Http\Requests\BaseRequest;

class SubmitAssessmentRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'response_id' => 'required|uuid|exists:assessment_responses,id',
            'answers' => 'required|array',
            'answers.*' => 'nullable', // Allow various answer types
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'response_id.required' => 'Assessment response ID is required',
            'response_id.exists' => 'Invalid assessment response ID',
            'answers.required' => 'Assessment answers are required',
            'answers.array' => 'Assessment answers must be an array',
        ]);
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return array_merge(parent::attributes(), [
            'response_id' => 'assessment response ID',
            'answers' => 'assessment answers',
        ]);
    }
}
