<?php

namespace App\Modules\Dashboard\Repositories;

use App\Models\User;
use App\Modules\Assessment\Models\AssessmentResponse;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardRepository implements DashboardRepositoryInterface
{
    /**
     * Get dashboard statistics for a specific role
     */
    public function getDashboardStats(string $role, int $userId): array
    {
        switch ($role) {
            case 'admin':
                return $this->getAdminStats();
            case 'guru':
                return $this->getGuruStats($userId);
            case 'orangtua':
                return $this->getOrangtuaStats($userId);
            case 'siswa':
            default:
                return $this->getSiswaStats($userId);
        }
    }

    /**
     * Get recent activities for dashboard
     */
    public function getRecentActivities(int $userId, int $limit = 10): array
    {
        return AssessmentResponse::with(['assessmentForm', 'user'])
            ->where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($response) {
                return [
                    'id' => $response->id,
                    'type' => 'assessment',
                    'title' => $response->assessmentForm->title ?? 'Assessment',
                    'description' => 'Assessment completed',
                    'score' => $response->total_score,
                    'status' => $response->status,
                    'created_at' => $response->created_at,
                    'formatted_date' => $response->created_at->diffForHumans(),
                ];
            })
            ->toArray();
    }

    /**
     * Get trend data for charts
     */
    public function getTrendData(string $role, int $userId, string $period = '6months'): array
    {
        $startDate = match ($period) {
            '3months' => Carbon::now()->subMonths(3),
            '6months' => Carbon::now()->subMonths(6),
            '1year' => Carbon::now()->subYear(),
            default => Carbon::now()->subMonths(6),
        };

        $query = AssessmentResponse::select(
            DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
            DB::raw('AVG(total_score) as average_score'),
            DB::raw('COUNT(*) as total_assessments')
        )
        ->where('created_at', '>=', $startDate)
        ->groupBy('month')
        ->orderBy('month');

        if ($role !== 'admin') {
            $query->where('user_id', $userId);
        }

        return $query->get()->map(function ($item) {
            return [
                'month' => Carbon::createFromFormat('Y-m', $item->month)->format('M Y'),
                'average_score' => round($item->average_score, 1),
                'total_assessments' => $item->total_assessments,
            ];
        })->toArray();
    }

    /**
     * Get role-specific data
     */
    public function getRoleSpecificData(string $role, int $userId): array
    {
        return match ($role) {
            'admin' => $this->getAdminSpecificData(),
            'guru' => $this->getGuruSpecificData($userId),
            'orangtua' => $this->getOrangtuaSpecificData($userId),
            'siswa' => $this->getSiswaSpecificData($userId),
            default => [],
        };
    }

    /**
     * Get monthly statistics
     */
    public function getMonthlyStats(int $userId, string $role): array
    {
        $currentMonth = Carbon::now()->startOfMonth();
        
        $query = AssessmentResponse::where('created_at', '>=', $currentMonth);
        
        if ($role !== 'admin') {
            $query->where('user_id', $userId);
        }

        $responses = $query->get();

        return [
            'total_assessments' => $responses->count(),
            'normal' => $responses->where('status', 'normal')->count(),
            'concern' => $responses->where('status', 'concern')->count(),
            'high_risk' => $responses->where('status', 'high_risk')->count(),
            'average_score' => $responses->avg('total_score') ?? 0,
        ];
    }

    /**
     * Get assessment summary
     */
    public function getAssessmentSummary(int $userId, string $role): array
    {
        $query = AssessmentResponse::query();
        
        if ($role !== 'admin') {
            $query->where('user_id', $userId);
        }

        $responses = $query->get();
        $latest = $query->latest()->first();

        return [
            'total_assessments' => $responses->count(),
            'latest_assessment' => $latest ? [
                'id' => $latest->id,
                'score' => $latest->total_score,
                'status' => $latest->status,
                'created_at' => $latest->created_at,
            ] : null,
            'status_distribution' => [
                'normal' => $responses->where('status', 'normal')->count(),
                'concern' => $responses->where('status', 'concern')->count(),
                'high_risk' => $responses->where('status', 'high_risk')->count(),
            ],
        ];
    }

    /**
     * Get admin specific statistics
     */
    private function getAdminStats(): array
    {
        return [
            'total_users' => User::count(),
            'total_siswa' => User::whereHas('roles', fn($q) => $q->where('name', 'siswa'))->count(),
            'total_guru' => User::whereHas('roles', fn($q) => $q->where('name', 'guru'))->count(),
            'total_orangtua' => User::whereHas('roles', fn($q) => $q->where('name', 'orangtua'))->count(),
            'total_assessments' => AssessmentResponse::count(),
            'monthly_assessments' => AssessmentResponse::whereMonth('created_at', Carbon::now()->month)->count(),
        ];
    }

    /**
     * Get guru specific statistics
     */
    private function getGuruStats(int $userId): array
    {
        // Assuming guru can see their students' data
        return [
            'total_students' => 0, // Implement based on your student-teacher relationship
            'monthly_assessments' => 0,
            'students_need_attention' => 0,
            'average_class_score' => 0,
        ];
    }

    /**
     * Get orangtua specific statistics
     */
    private function getOrangtuaStats(int $userId): array
    {
        // Assuming orangtua can see their children's data
        return [
            'total_children' => 0, // Implement based on your parent-child relationship
            'children_assessments' => 0,
            'children_need_attention' => 0,
            'latest_child_score' => 0,
        ];
    }

    /**
     * Get siswa specific statistics
     */
    private function getSiswaStats(int $userId): array
    {
        $responses = AssessmentResponse::where('user_id', $userId)->get();
        $latest = $responses->sortByDesc('created_at')->first();

        return [
            'total_assessments' => $responses->count(),
            'latest_score' => $latest->total_score ?? 0,
            'latest_status' => $latest->status ?? 'normal',
            'monthly_count' => $responses->where('created_at', '>=', Carbon::now()->startOfMonth())->count(),
        ];
    }

    /**
     * Get admin specific data
     */
    private function getAdminSpecificData(): array
    {
        return [
            'recent_registrations' => User::latest()->limit(5)->get()->toArray(),
            'system_health' => [
                'database_status' => 'healthy',
                'cache_status' => 'healthy',
                'queue_status' => 'healthy',
            ],
        ];
    }

    /**
     * Get guru specific data
     */
    private function getGuruSpecificData(int $userId): array
    {
        return [
            'class_performance' => [],
            'student_alerts' => [],
        ];
    }

    /**
     * Get orangtua specific data
     */
    private function getOrangtuaSpecificData(int $userId): array
    {
        return [
            'children_progress' => [],
            'upcoming_assessments' => [],
        ];
    }

    /**
     * Get siswa specific data
     */
    private function getSiswaSpecificData(int $userId): array
    {
        return [
            'assessment_history' => AssessmentResponse::where('user_id', $userId)
                ->with('assessmentForm')
                ->latest()
                ->limit(10)
                ->get()
                ->toArray(),
            'recommendations' => [],
        ];
    }
}
