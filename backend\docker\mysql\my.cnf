[mysqld]
general_log = 1
general_log_file = /var/lib/mysql/general.log
default-authentication-plugin=mysql_native_password

# Performance settings
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 1
innodb_lock_wait_timeout = 120

# Character set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Connection settings
max_connections = 200
max_allowed_packet = 64M

# Query cache
query_cache_type = 1
query_cache_size = 32M

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
