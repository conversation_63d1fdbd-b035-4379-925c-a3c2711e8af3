<!DOCTYPE html>
<html lang="id" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Dashboard - SantriMental</title>

    <!-- Modern CSS Framework -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css']); ?>

    <!-- External Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">
</head>
<body class="gradient-mesh min-h-screen transition-all duration-300">

    <!-- Mobile Menu Overlay -->
    <div id="mobile-overlay" class="fixed inset-0 bg-black/50 backdrop-filter blur-sm z-40 lg:hidden hidden transition-all duration-300"></div>

    <!-- Modern Sidebar -->
    <aside id="sidebar" class="fixed left-0 top-0 h-full w-72 sidebar-modern z-50 transform -translate-x-full lg:translate-x-0 transition-transform duration-300">
        <div class="flex flex-col h-full">
            <!-- Logo Section -->
            <div class="p-6 border-b border-white/10">
                <div class="flex items-center">
                    <div class="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center mr-4 shadow-lg">
                        <i data-lucide="brain" class="w-6 h-6 text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-white">SantriMental</h1>
                        <p class="text-sm text-white/60">Mental Health Dashboard</p>
                    </div>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="flex-1 p-6 space-y-2">
                <div class="mb-6">
                    <p class="text-xs font-semibold text-white/40 uppercase tracking-wider mb-3">Main Menu</p>

                    <a href="#" data-page="dashboard" id="dashboard-menu" class="sidebar-item active">
                        <i data-lucide="layout-dashboard" class="sidebar-icon"></i>
                        <span class="font-medium">Dashboard</span>
                        <div class="ml-auto">
                            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        </div>
                    </a>

                    <a href="<?php echo e(route('assessments')); ?>" id="assessment-menu" class="sidebar-item">
                        <i data-lucide="clipboard-check" class="sidebar-icon"></i>
                        <span class="font-medium">Assessment</span>
                        <div class="ml-auto">
                            <span class="badge badge-primary text-xs">New</span>
                        </div>
                    </a>

                    <a href="<?php echo e(route('history')); ?>" id="history-menu" class="sidebar-item">
                        <i data-lucide="history" class="sidebar-icon"></i>
                        <span class="font-medium">Riwayat</span>
                    </a>
                </div>

                <!-- Role-specific menu items will be inserted here -->
                <div id="role-specific-menu" class="space-y-2"></div>

                <div class="border-t border-white/10 pt-4 mt-6">
                    <p class="text-xs font-semibold text-white/40 uppercase tracking-wider mb-3">Account</p>

                    <a href="#" data-page="profile" class="sidebar-item">
                        <i data-lucide="user" class="sidebar-icon"></i>
                        <span class="font-medium">Profil</span>
                    </a>

                    <a href="#" data-page="settings" class="sidebar-item">
                        <i data-lucide="settings" class="sidebar-icon"></i>
                        <span class="font-medium">Pengaturan</span>
                    </a>
                </div>
            </nav>

            <!-- User Profile Section -->
            <div class="p-6 border-t border-white/10">
                <div class="glass-card p-4 rounded-xl">
                    <div class="flex items-center">
                        <div id="user-avatar" class="w-12 h-12 gradient-secondary rounded-xl flex items-center justify-center mr-3 shadow-lg">
                            <span id="user-initials" class="text-white font-bold text-sm">DU</span>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p id="user-name" class="text-white font-semibold text-sm truncate">Demo User</p>
                            <p id="user-email" class="text-white/60 text-xs truncate"><EMAIL></p>
                            <div class="flex items-center mt-1">
                                <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                                <span class="text-xs text-green-400 font-medium">Online</span>
                            </div>
                        </div>
                        <div class="flex flex-col space-y-1">
                            <button id="theme-toggle" class="p-2 text-white/70 hover:text-white transition-colors rounded-lg hover:bg-white/10" data-tooltip="Toggle Theme">
                                <i data-lucide="moon" class="w-4 h-4"></i>
                            </button>
                            <button id="logout-btn" class="p-2 text-white/70 hover:text-red-400 transition-colors rounded-lg hover:bg-red-500/10" data-tooltip="Logout">
                                <i data-lucide="log-out" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="lg:ml-72 min-h-screen transition-all duration-300">
        <!-- Modern Header -->
        <header class="header-modern sticky top-0 z-30">
            <div class="flex items-center justify-between p-6">
                <div class="flex items-center">
                    <button id="mobile-menu-btn" class="lg:hidden p-2 text-white/70 hover:text-white transition-colors rounded-lg hover:bg-white/10 mr-4">
                        <i data-lucide="menu" class="w-5 h-5"></i>
                    </button>
                    <div>
                        <div class="flex items-center space-x-3">
                            <h1 id="page-title" class="text-3xl font-bold text-white">Dashboard</h1>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                <span class="text-sm text-green-400 font-medium">Live</span>
                            </div>
                        </div>
                        <p id="page-subtitle" class="text-white/60 mt-1">Selamat datang kembali! Berikut ringkasan kesehatan mental Anda.</p>
                        <div class="flex items-center space-x-4 mt-2">
                            <div class="flex items-center text-sm text-white/50">
                                <i data-lucide="calendar" class="w-4 h-4 mr-1"></i>
                                <span id="current-date"></span>
                            </div>
                            <div class="flex items-center text-sm text-white/50">
                                <i data-lucide="clock" class="w-4 h-4 mr-1"></i>
                                <span id="current-time"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex items-center space-x-3">
                    <!-- Quick Actions -->
                    <button class="btn-ghost p-3 rounded-xl" data-tooltip="Quick Assessment">
                        <i data-lucide="zap" class="w-5 h-5"></i>
                    </button>

                    <!-- Notifications -->
                    <button class="btn-ghost p-3 rounded-xl relative" data-tooltip="Notifications">
                        <i data-lucide="bell" class="w-5 h-5"></i>
                        <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
                            <span class="text-xs text-white font-bold">3</span>
                        </div>
                    </button>

                    <!-- Search -->
                    <button class="btn-ghost p-3 rounded-xl" data-tooltip="Search">
                        <i data-lucide="search" class="w-5 h-5"></i>
                    </button>

                    <!-- Settings -->
                    <button class="btn-ghost p-3 rounded-xl" data-tooltip="Settings">
                        <i data-lucide="settings" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>

            <!-- Breadcrumb -->
            <div class="px-6 pb-4">
                <nav class="flex items-center space-x-2 text-sm">
                    <a href="#" class="text-white/60 hover:text-white transition-colors">Home</a>
                    <i data-lucide="chevron-right" class="w-4 h-4 text-white/40"></i>
                    <span class="text-white font-medium">Dashboard</span>
                </nav>
            </div>
        </header>

        <!-- Page Content -->
        <div class="p-6 space-y-8">

            <!-- Dashboard Content -->
            <div id="dashboard-content" class="page-content">

                <!-- Modern Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <!-- Total Assessments Card -->
                    <div class="stats-card animate-fade-in-up" style="animation-delay: 0.1s;">
                        <div class="flex items-center justify-between mb-4">
                            <div class="p-3 gradient-primary rounded-xl shadow-lg">
                                <i data-lucide="clipboard-check" class="w-6 h-6 text-white"></i>
                            </div>
                            <div class="stats-change positive">
                                <i data-lucide="trending-up" class="w-3 h-3 mr-1"></i>
                                +12%
                            </div>
                        </div>
                        <div>
                            <p class="stats-label">Total Skrining</p>
                            <p id="total-assessments" class="stats-value">15</p>
                            <p class="text-xs text-white/50 mt-1">Sejak bergabung</p>
                        </div>
                    </div>

                    <!-- Latest Score Card -->
                    <div class="stats-card animate-fade-in-up" style="animation-delay: 0.2s;">
                        <div class="flex items-center justify-between mb-4">
                            <div class="p-3 gradient-success rounded-xl shadow-lg">
                                <i data-lucide="target" class="w-6 h-6 text-white"></i>
                            </div>
                            <div class="stats-change positive">
                                <i data-lucide="trending-up" class="w-3 h-3 mr-1"></i>
                                Baik
                            </div>
                        </div>
                        <div>
                            <p class="stats-label">Skor Terakhir</p>
                            <p id="latest-score" class="stats-value">4</p>
                            <p class="text-xs text-white/50 mt-1">Dari 20 poin</p>
                        </div>
                    </div>

                    <!-- Status Card -->
                    <div class="stats-card animate-fade-in-up" style="animation-delay: 0.3s;">
                        <div class="flex items-center justify-between mb-4">
                            <div class="p-3 gradient-secondary rounded-xl shadow-lg">
                                <i data-lucide="heart-pulse" class="w-6 h-6 text-white"></i>
                            </div>
                            <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                        </div>
                        <div>
                            <p class="stats-label">Status Mental</p>
                            <p id="latest-status" class="text-2xl font-bold text-green-400 mb-1">Normal</p>
                            <p class="text-xs text-white/50">Kondisi stabil</p>
                        </div>
                    </div>

                    <!-- Monthly Count Card -->
                    <div class="stats-card animate-fade-in-up" style="animation-delay: 0.4s;">
                        <div class="flex items-center justify-between mb-4">
                            <div class="p-3 gradient-warning rounded-xl shadow-lg">
                                <i data-lucide="calendar-days" class="w-6 h-6 text-white"></i>
                            </div>
                            <div class="stats-change positive">
                                <i data-lucide="plus" class="w-3 h-3 mr-1"></i>
                                +2
                            </div>
                        </div>
                        <div>
                            <p class="stats-label">Bulan Ini</p>
                            <p id="monthly-count" class="stats-value">5</p>
                            <p class="text-xs text-white/50 mt-1">Assessment selesai</p>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="modern-card p-6 animate-fade-in-up" style="animation-delay: 0.5s;">
                        <div class="flex items-center mb-4">
                            <div class="p-3 bg-blue-500/20 rounded-xl mr-4">
                                <i data-lucide="zap" class="w-6 h-6 text-blue-400"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-white">Quick Assessment</h3>
                                <p class="text-sm text-white/60">Mulai skrining cepat</p>
                            </div>
                        </div>
                        <button class="btn-primary w-full">
                            <i data-lucide="play" class="w-4 h-4 mr-2"></i>
                            Mulai Sekarang
                        </button>
                    </div>

                    <div class="modern-card p-6 animate-fade-in-up" style="animation-delay: 0.6s;">
                        <div class="flex items-center mb-4">
                            <div class="p-3 bg-purple-500/20 rounded-xl mr-4">
                                <i data-lucide="book-open" class="w-6 h-6 text-purple-400"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-white">Panduan Kesehatan</h3>
                                <p class="text-sm text-white/60">Tips & artikel</p>
                            </div>
                        </div>
                        <button class="btn-secondary w-full">
                            <i data-lucide="external-link" class="w-4 h-4 mr-2"></i>
                            Baca Artikel
                        </button>
                    </div>

                    <div class="modern-card p-6 animate-fade-in-up" style="animation-delay: 0.7s;">
                        <div class="flex items-center mb-4">
                            <div class="p-3 bg-green-500/20 rounded-xl mr-4">
                                <i data-lucide="phone" class="w-6 h-6 text-green-400"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-white">Konsultasi</h3>
                                <p class="text-sm text-white/60">Hubungi konselor</p>
                            </div>
                        </div>
                        <button class="btn-ghost w-full">
                            <i data-lucide="message-circle" class="w-4 h-4 mr-2"></i>
                            Chat Sekarang
                        </button>
                    </div>
                </div>

                <!-- Modern Charts Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Score Trend Chart -->
                    <div class="chart-container animate-fade-in-up" style="animation-delay: 0.8s;">
                        <div class="chart-header">
                            <div>
                                <h4 class="chart-title">Tren Skor SRQ-20</h4>
                                <p class="chart-subtitle">Perkembangan skor dalam 6 bulan terakhir</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button class="btn-ghost p-2 rounded-lg" data-tooltip="Export Chart">
                                    <i data-lucide="download" class="w-4 h-4"></i>
                                </button>
                                <button class="btn-ghost p-2 rounded-lg" data-tooltip="Fullscreen">
                                    <i data-lucide="maximize" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                        <div class="h-64 relative">
                            <canvas id="score-trend-chart"></canvas>
                            <div id="trend-loading" class="absolute inset-0 flex items-center justify-center">
                                <div class="loading-spinner"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Distribution -->
                    <div class="chart-container animate-fade-in-up" style="animation-delay: 0.9s;">
                        <div class="chart-header">
                            <div>
                                <h4 class="chart-title">Distribusi Status Mental</h4>
                                <p class="chart-subtitle">Persentase kondisi mental bulan ini</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button class="btn-ghost p-2 rounded-lg" data-tooltip="View Details">
                                    <i data-lucide="info" class="w-4 h-4"></i>
                                </button>
                                <button class="btn-ghost p-2 rounded-lg" data-tooltip="Share">
                                    <i data-lucide="share-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                        <div class="h-64 relative">
                            <canvas id="monthly-chart"></canvas>
                            <div id="monthly-loading" class="absolute inset-0 flex items-center justify-center">
                                <div class="loading-spinner"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity Section -->
                <div class="modern-card p-6 animate-fade-in-up" style="animation-delay: 1.0s;">
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h4 class="text-xl font-bold text-white">Aktivitas Terbaru</h4>
                            <p class="text-white/60 text-sm">Riwayat assessment dan aktivitas terkini</p>
                        </div>
                        <button class="btn-ghost">
                            <i data-lucide="external-link" class="w-4 h-4 mr-2"></i>
                            Lihat Semua
                        </button>
                    </div>

                    <div id="recent-activity" class="space-y-4">
                        <!-- Activity items will be loaded here -->
                        <div class="skeleton h-16 rounded-lg"></div>
                        <div class="skeleton h-16 rounded-lg"></div>
                        <div class="skeleton h-16 rounded-lg"></div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="glass-card rounded-xl p-6 fade-in" style="animation-delay: 0.6s;">
                    <h4 class="text-lg font-semibold text-white mb-4">Aktivitas Terbaru</h4>
                    <div id="recent-activity" class="space-y-4">
                        <!-- Activity items will be inserted here -->
                    </div>
                </div>

            </div>

            <!-- Other pages will be loaded here -->
            <div id="assessment-page" class="page-content hidden">
                <div class="glass-card rounded-xl p-6 text-center">
                    <h3 class="text-xl font-bold text-white mb-4">Assessment Kesehatan Mental</h3>
                    <p class="text-purple-200 mb-6">Pilih jenis assessment yang ingin Anda lakukan</p>
                    <button onclick="window.location.href='<?php echo e(route('assessments')); ?>'" class="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300">
                        Pilih Assessment
                    </button>
                </div>
            </div>

            <div id="history-page" class="page-content hidden">
                <div class="glass-card rounded-xl p-6">
                    <h3 class="text-xl font-bold text-white mb-4">Riwayat Tes</h3>
                    <p class="text-purple-200">Riwayat tes Anda akan ditampilkan di sini</p>
                </div>
            </div>

            <div id="analytics-page" class="page-content hidden">
                <div class="glass-card rounded-xl p-6">
                    <h3 class="text-xl font-bold text-white mb-4">Analitik</h3>
                    <p class="text-purple-200">Analitik mendalam akan ditampilkan di sini</p>
                </div>
            </div>

            <div id="profile-page" class="page-content hidden">
                <div class="glass-card rounded-xl p-6">
                    <h3 class="text-xl font-bold text-white mb-4">Profil Pengguna</h3>
                    <p class="text-purple-200">Pengaturan profil akan ditampilkan di sini</p>
                </div>
            </div>

        </div>
    </main>

    <!-- JavaScript Libraries -->
    <script src="<?php echo e(asset('js/auth.js')); ?>"></script>
    <script src="<?php echo e(asset('js/utils.js')); ?>"></script>
    <script src="<?php echo e(asset('js/performance-optimizer.js')); ?>"></script>
    <script src="<?php echo e(asset('js/modern-dashboard.js')); ?>"></script>
    <script src="<?php echo e(asset('js/modern-components.js')); ?>"></script>
    <script src="<?php echo e(asset('js/dashboard.js')); ?>"></script>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Initialize modern dashboard features
        document.addEventListener('DOMContentLoaded', () => {
            // Update current date and time
            function updateDateTime() {
                const now = new Date();
                const dateEl = document.getElementById('current-date');
                const timeEl = document.getElementById('current-time');

                if (dateEl) {
                    dateEl.textContent = now.toLocaleDateString('id-ID', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    });
                }

                if (timeEl) {
                    timeEl.textContent = now.toLocaleTimeString('id-ID', {
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                }
            }

            updateDateTime();
            setInterval(updateDateTime, 60000); // Update every minute

            // Initialize chart loading states
            setTimeout(() => {
                const trendLoading = document.getElementById('trend-loading');
                const monthlyLoading = document.getElementById('monthly-loading');

                if (trendLoading) trendLoading.style.display = 'none';
                if (monthlyLoading) monthlyLoading.style.display = 'none';
            }, 2000);

            // Animate stats values
            const statsValues = document.querySelectorAll('.stats-value');
            statsValues.forEach(el => {
                const finalValue = parseInt(el.textContent);
                if (!isNaN(finalValue)) {
                    window.modernDashboard.animateValue(el, 0, finalValue, 1500);
                }
            });

            // Add hover effects to cards
            const cards = document.querySelectorAll('.stats-card, .modern-card, .chart-container');
            cards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    card.style.transform = 'translateY(-4px)';
                });

                card.addEventListener('mouseleave', () => {
                    card.style.transform = 'translateY(0)';
                });
            });

            // Add click handlers for quick actions
            document.querySelectorAll('.btn-primary, .btn-secondary').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    if (btn.textContent.includes('Mulai Sekarang')) {
                        window.modernComponents.showNotification(
                            'Redirecting to assessment...',
                            'info',
                            2000
                        );
                        setTimeout(() => {
                            window.location.href = '<?php echo e(route("assessments")); ?>';
                        }, 1000);
                    } else if (btn.textContent.includes('Baca Artikel')) {
                        window.modernComponents.showModal('health-guide-modal', {
                            title: 'Panduan Kesehatan Mental',
                            content: `
                                <div class="space-y-4">
                                    <p class="text-slate-300">Berikut adalah beberapa tips untuk menjaga kesehatan mental:</p>
                                    <ul class="list-disc list-inside space-y-2 text-slate-400">
                                        <li>Lakukan olahraga secara teratur</li>
                                        <li>Tidur yang cukup (7-8 jam per hari)</li>
                                        <li>Konsumsi makanan bergizi</li>
                                        <li>Kelola stress dengan baik</li>
                                        <li>Jaga hubungan sosial yang positif</li>
                                    </ul>
                                </div>
                            `,
                            actions: [
                                {
                                    label: 'Tutup',
                                    class: 'btn-ghost',
                                    onClick: 'modernComponents.closeModal("health-guide-modal")'
                                }
                            ]
                        });
                    } else if (btn.textContent.includes('Chat Sekarang')) {
                        window.modernComponents.showNotification(
                            'Fitur konsultasi akan segera tersedia',
                            'info',
                            3000,
                            {
                                description: 'Kami sedang mengembangkan fitur ini untuk memberikan layanan terbaik'
                            }
                        );
                    }
                });
            });

            // Demo notification for live features
            setTimeout(() => {
                window.modernComponents.showNotification(
                    'Dashboard berhasil dimuat!',
                    'success',
                    3000,
                    {
                        description: 'Semua data telah diperbarui dengan informasi terkini'
                    }
                );
            }, 2000);
        });
    </script>
</body>
</html>
<?php /**PATH C:\laragon\www\santrimental\backend\resources\views/dashboard.blade.php ENDPATH**/ ?>