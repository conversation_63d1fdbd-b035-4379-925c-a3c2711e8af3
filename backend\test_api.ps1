# Assessment API Test Script
$baseUrl = "http://localhost:8000/api"
$token = "1|V7mBeIxV14MUkpuwFnq7Q75smLPh8Ze0dysDYxj64920b203"
$headers = @{
    "Accept" = "application/json"
    "Content-Type" = "application/json"
    "Authorization" = "Bearer $token"
}

Write-Host "=== Testing Assessment API ===" -ForegroundColor Green

# Test 1: Get all assessment forms
Write-Host "`n1. Testing GET /api/assessments" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/assessments" -Method GET -Headers $headers
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "Found $($data.data.Count) assessment forms" -ForegroundColor Green
    foreach ($form in $data.data) {
        Write-Host "  - $($form.code): $($form.name)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Get available forms
Write-Host "`n2. Testing GET /api/assessments/available" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/assessments/available" -Method GET -Headers $headers
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "Available forms: $($data.data.Count)" -ForegroundColor Green
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Get specific form details
Write-Host "`n3. Testing GET /api/assessments/SRQ20" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/assessments/SRQ20" -Method GET -Headers $headers
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "Form: $($data.data.name)" -ForegroundColor Green
    Write-Host "Questions: $($data.data.questions_count)" -ForegroundColor Green
    Write-Host "Time limit: $($data.data.time_limit) minutes" -ForegroundColor Green
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Start assessment
Write-Host "`n4. Testing POST /api/assessments/start" -ForegroundColor Yellow
try {
    $startData = @{
        form_code = "SRQ20"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "$baseUrl/assessments/start" -Method POST -Headers $headers -Body $startData
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    $responseId = $data.data.id
    Write-Host "Assessment started with ID: $responseId" -ForegroundColor Green
    Write-Host "Status: $($data.data.status)" -ForegroundColor Green
    
    # Test 5: Submit assessment
    Write-Host "`n5. Testing POST /api/assessments/submit" -ForegroundColor Yellow
    $answers = @{
        response_id = $responseId
        answers = @{
            question_0 = "yes"
            question_1 = "no"
            question_2 = "yes"
            question_3 = "no"
            question_4 = "no"
            question_5 = "yes"
            question_6 = "no"
            question_7 = "yes"
            question_8 = "yes"
            question_9 = "no"
            question_10 = "yes"
            question_11 = "yes"
            question_12 = "no"
            question_13 = "no"
            question_14 = "yes"
            question_15 = "no"
            question_16 = "no"
            question_17 = "yes"
            question_18 = "no"
            question_19 = "yes"
        }
    } | ConvertTo-Json -Depth 3
    
    $submitResponse = Invoke-WebRequest -Uri "$baseUrl/assessments/submit" -Method POST -Headers $headers -Body $answers
    Write-Host "Status: $($submitResponse.StatusCode)" -ForegroundColor Green
    $submitData = $submitResponse.Content | ConvertFrom-Json
    Write-Host "Assessment completed!" -ForegroundColor Green
    Write-Host "Total Score: $($submitData.data.total_score)" -ForegroundColor Green
    Write-Host "Interpretation: $($submitData.data.interpretation_label)" -ForegroundColor Green
    Write-Host "Level: $($submitData.data.interpretation_level)" -ForegroundColor Green
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Get user history
Write-Host "`n6. Testing GET /api/assessments/history" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/assessments/history" -Method GET -Headers $headers
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "Assessment history: $($data.data.Count) records" -ForegroundColor Green
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
