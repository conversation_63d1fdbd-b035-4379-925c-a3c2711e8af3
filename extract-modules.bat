@echo off
echo ========================================
echo SantriMental Module Extractor
echo ========================================
echo.

set "SOURCE_DIR=%cd%\backend"
set "MODULES_DIR=%cd%\santrimental-modules"

echo Creating module extraction directory...
if exist "%MODULES_DIR%" rmdir /s /q "%MODULES_DIR%"
mkdir "%MODULES_DIR%"

echo.
echo [1/8] Extracting Frontend Views...
mkdir "%MODULES_DIR%\resources\views"
copy "%SOURCE_DIR%\resources\views\index.blade.php" "%MODULES_DIR%\resources\views\"
copy "%SOURCE_DIR%\resources\views\dashboard.blade.php" "%MODULES_DIR%\resources\views\"
copy "%SOURCE_DIR%\resources\views\admin-dashboard.blade.php" "%MODULES_DIR%\resources\views\"
copy "%SOURCE_DIR%\resources\views\guru-dashboard.blade.php" "%MODULES_DIR%\resources\views\"
copy "%SOURCE_DIR%\resources\views\orangtua-dashboard.blade.php" "%MODULES_DIR%\resources\views\"
copy "%SOURCE_DIR%\resources\views\assessments.blade.php" "%MODULES_DIR%\resources\views\"
copy "%SOURCE_DIR%\resources\views\history.blade.php" "%MODULES_DIR%\resources\views\"
copy "%SOURCE_DIR%\resources\views\dynamic-form.blade.php" "%MODULES_DIR%\resources\views\"
copy "%SOURCE_DIR%\resources\views\srq20-form.blade.php" "%MODULES_DIR%\resources\views\"

echo [2/8] Extracting JavaScript Modules...
mkdir "%MODULES_DIR%\public\js"
copy "%SOURCE_DIR%\public\js\auth.js" "%MODULES_DIR%\public\js\"
copy "%SOURCE_DIR%\public\js\dashboard.js" "%MODULES_DIR%\public\js\"
copy "%SOURCE_DIR%\public\js\dynamic-form.js" "%MODULES_DIR%\public\js\"
copy "%SOURCE_DIR%\public\js\history.js" "%MODULES_DIR%\public\js\"
copy "%SOURCE_DIR%\public\js\modern-components.js" "%MODULES_DIR%\public\js\"
copy "%SOURCE_DIR%\public\js\modern-dashboard.js" "%MODULES_DIR%\public\js\"
copy "%SOURCE_DIR%\public\js\performance-optimizer.js" "%MODULES_DIR%\public\js\"
copy "%SOURCE_DIR%\public\js\srq20-form.js" "%MODULES_DIR%\public\js\"
copy "%SOURCE_DIR%\public\js\utils.js" "%MODULES_DIR%\public\js\"

echo [3/8] Extracting CSS Framework...
mkdir "%MODULES_DIR%\public\css"
copy "%SOURCE_DIR%\public\css\modern-dashboard.css" "%MODULES_DIR%\public\css\"

echo [4/8] Extracting Controllers...
mkdir "%MODULES_DIR%\app\Http\Controllers\Api"
copy "%SOURCE_DIR%\app\Http\Controllers\Api\AuthController.php" "%MODULES_DIR%\app\Http\Controllers\Api\"
copy "%SOURCE_DIR%\app\Http\Controllers\Api\AssessmentController.php" "%MODULES_DIR%\app\Http\Controllers\Api\"
copy "%SOURCE_DIR%\app\Http\Controllers\Api\FormTemplateController.php" "%MODULES_DIR%\app\Http\Controllers\Api\"
copy "%SOURCE_DIR%\app\Http\Controllers\Api\RoleController.php" "%MODULES_DIR%\app\Http\Controllers\Api\"

echo [5/8] Extracting Models...
mkdir "%MODULES_DIR%\app\Models"
copy "%SOURCE_DIR%\app\Models\Assessment.php" "%MODULES_DIR%\app\Models\"
copy "%SOURCE_DIR%\app\Models\AssessmentAnswer.php" "%MODULES_DIR%\app\Models\"
copy "%SOURCE_DIR%\app\Models\FormQuestion.php" "%MODULES_DIR%\app\Models\"
copy "%SOURCE_DIR%\app\Models\FormResponse.php" "%MODULES_DIR%\app\Models\"
copy "%SOURCE_DIR%\app\Models\FormTemplate.php" "%MODULES_DIR%\app\Models\"
copy "%SOURCE_DIR%\app\Models\Role.php" "%MODULES_DIR%\app\Models\"
copy "%SOURCE_DIR%\app\Models\StudentParentRelationship.php" "%MODULES_DIR%\app\Models\"
copy "%SOURCE_DIR%\app\Models\User.php" "%MODULES_DIR%\app\Models\"

echo [6/8] Extracting Migrations...
mkdir "%MODULES_DIR%\database\migrations"
copy "%SOURCE_DIR%\database\migrations\2025_07_11_023139_create_assessments_table.php" "%MODULES_DIR%\database\migrations\"
copy "%SOURCE_DIR%\database\migrations\2025_07_11_023140_create_assessment_answers_table.php" "%MODULES_DIR%\database\migrations\"
copy "%SOURCE_DIR%\database\migrations\2025_07_12_154124_create_form_templates_table.php" "%MODULES_DIR%\database\migrations\"
copy "%SOURCE_DIR%\database\migrations\2025_07_13_072309_create_roles_table.php" "%MODULES_DIR%\database\migrations\"
copy "%SOURCE_DIR%\database\migrations\2025_07_13_072328_create_student_parent_relationships_table.php" "%MODULES_DIR%\database\migrations\"
copy "%SOURCE_DIR%\database\migrations\2025_07_13_073026_add_additional_fields_to_users_table.php" "%MODULES_DIR%\database\migrations\"
copy "%SOURCE_DIR%\database\migrations\2025_07_19_045634_create_assessment_forms_table.php" "%MODULES_DIR%\database\migrations\"
copy "%SOURCE_DIR%\database\migrations\2025_07_19_045644_create_assessment_responses_table.php" "%MODULES_DIR%\database\migrations\"

echo [7/8] Extracting Seeders...
mkdir "%MODULES_DIR%\database\seeders"
copy "%SOURCE_DIR%\database\seeders\AssessmentFormSeeder.php" "%MODULES_DIR%\database\seeders\"
copy "%SOURCE_DIR%\database\seeders\FormTemplateSeeder.php" "%MODULES_DIR%\database\seeders\"
copy "%SOURCE_DIR%\database\seeders\RoleSeeder.php" "%MODULES_DIR%\database\seeders\"
copy "%SOURCE_DIR%\database\seeders\StudentParentRelationshipSeeder.php" "%MODULES_DIR%\database\seeders\"
copy "%SOURCE_DIR%\database\seeders\UserSeeder.php" "%MODULES_DIR%\database\seeders\"

echo [8/8] Extracting Middleware and Routes...
mkdir "%MODULES_DIR%\app\Http\Middleware"
copy "%SOURCE_DIR%\app\Http\Middleware\RoleMiddleware.php" "%MODULES_DIR%\app\Http\Middleware\"

mkdir "%MODULES_DIR%\routes"
copy "%SOURCE_DIR%\routes\web.php" "%MODULES_DIR%\routes\"
copy "%SOURCE_DIR%\routes\api.php" "%MODULES_DIR%\routes\"

echo.
echo Extracting Modular Structure...
if exist "%SOURCE_DIR%\app\Modules" (
    mkdir "%MODULES_DIR%\app\Modules"
    xcopy "%SOURCE_DIR%\app\Modules" "%MODULES_DIR%\app\Modules" /E /I /H /Y
)

echo.
echo Creating configuration files...
mkdir "%MODULES_DIR%\config"

echo.
echo ========================================
echo Module extraction complete!
echo ========================================
echo.
echo Extracted modules location: %MODULES_DIR%
echo.
echo Next: Run create-installer.bat to create installation package
echo.
pause
