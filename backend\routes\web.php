<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('index');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->name('dashboard');

// Role-specific dashboards
Route::get('/admin/dashboard', function () {
    return view('admin-dashboard');
})->name('admin.dashboard');

Route::get('/guru/dashboard', function () {
    return view('guru-dashboard');
})->name('guru.dashboard');

Route::get('/orangtua/dashboard', function () {
    return view('orangtua-dashboard');
})->name('orangtua.dashboard');

Route::get('/assessments', function () {
    return view('assessments');
})->name('assessments');

Route::get('/assessment/{code}', function ($code) {
    return view('dynamic-form', compact('code'));
})->name('dynamic-form');

Route::get('/srq20-form', function () {
    return redirect()->route('dynamic-form', ['code' => 'SRQ20']);
})->name('srq20-form');

Route::get('/history', function () {
    return view('history');
})->name('history');
