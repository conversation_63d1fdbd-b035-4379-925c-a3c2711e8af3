<?php

use App\Modules\Dashboard\Http\Controllers\DashboardController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Dashboard Module API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for the Dashboard module. These routes are
| automatically loaded by the DashboardServiceProvider.
|
*/

Route::prefix('api/dashboard')->middleware(['auth:sanctum'])->group(function () {
    // Main dashboard data
    Route::get('/', [DashboardController::class, 'index'])->name('api.dashboard.index');
    
    // Dashboard components
    Route::get('/statistics', [DashboardController::class, 'statistics'])->name('api.dashboard.statistics');
    Route::get('/charts', [DashboardController::class, 'charts'])->name('api.dashboard.charts');
    Route::get('/activities', [DashboardController::class, 'activities'])->name('api.dashboard.activities');
    Route::get('/widgets', [DashboardController::class, 'widgets'])->name('api.dashboard.widgets');
    
    // Cache management
    Route::delete('/cache', [DashboardController::class, 'clearCache'])->name('api.dashboard.clear-cache');
});

// Legacy support for existing API calls
Route::prefix('api')->middleware(['auth:sanctum'])->group(function () {
    // Redirect old dashboard endpoints to new module
    Route::get('/dashboard/role-data', [DashboardController::class, 'index']);
    Route::get('/dashboard/stats', [DashboardController::class, 'statistics']);
});
