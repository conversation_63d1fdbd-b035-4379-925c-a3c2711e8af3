<?php

namespace App\Modules\Assessment\Models;

use App\Core\Traits\HasUuidPrimaryKey;
use App\Core\Traits\HasTimestamps;
use App\Core\Traits\HasActiveStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * AssessmentForm Model
 * 
 * @property string $id
 * @property string $code
 * @property string $name
 * @property string $description
 * @property string $category
 * @property array $questions
 * @property array $scoring_rules
 * @property array $interpretation_rules
 * @property int $time_limit
 * @property bool $is_active
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon $deleted_at
 */
class AssessmentForm extends Model
{
    use HasFactory, HasUuidPrimaryKey, HasTimestamps, HasActiveStatus, SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'assessment_forms';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'code',
        'name',
        'description',
        'category',
        'questions',
        'scoring_rules',
        'interpretation_rules',
        'time_limit',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'questions' => 'array',
        'scoring_rules' => 'array',
        'interpretation_rules' => 'array',
        'time_limit' => 'integer',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * Assessment form codes
     */
    public const CODES = [
        'SRQ20' => 'SRQ20',
        'MHKQ' => 'MHKQ',
        'PDD' => 'PDD',
        'GSE' => 'GSE',
        'MSCS' => 'MSCS',
        'PHQ9' => 'PHQ9',
        'DASS42' => 'DASS42',
    ];

    /**
     * Assessment categories
     */
    public const CATEGORIES = [
        'mental_health' => 'Mental Health',
        'self_efficacy' => 'Self Efficacy',
        'self_care' => 'Self Care',
        'knowledge' => 'Knowledge',
        'discrimination' => 'Discrimination',
    ];

    /**
     * Validation rules for the model.
     */
    public static function rules(): array
    {
        return [
            'code' => 'required|string|max:20|unique:assessment_forms,code',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category' => 'required|string|max:50',
            'questions' => 'required|array|min:1',
            'questions.*.text' => 'required|string',
            'questions.*.type' => 'required|string|in:likert,multiple_choice,yes_no',
            'questions.*.options' => 'required_if:questions.*.type,multiple_choice|array',
            'scoring_rules' => 'required|array',
            'interpretation_rules' => 'required|array',
            'time_limit' => 'nullable|integer|min:1|max:180',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get assessment responses
     */
    public function responses(): HasMany
    {
        return $this->hasMany(AssessmentResponse::class, 'form_id');
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'code';
    }

    /**
     * Scope to filter by category
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to filter by code
     */
    public function scopeByCode($query, string $code)
    {
        return $query->where('code', $code);
    }

    /**
     * Get questions count
     */
    public function getQuestionsCountAttribute(): int
    {
        return count($this->questions ?? []);
    }

    /**
     * Get estimated completion time in minutes
     */
    public function getEstimatedTimeAttribute(): int
    {
        return $this->time_limit ?? ($this->questions_count * 0.5); // 30 seconds per question
    }

    /**
     * Calculate score based on answers
     */
    public function calculateScore(array $answers): array
    {
        $scoring = $this->scoring_rules;
        $totalScore = 0;
        $maxScore = 0;
        $subscores = [];

        foreach ($this->questions as $index => $question) {
            $questionKey = "question_{$index}";
            $answer = $answers[$questionKey] ?? null;

            if ($answer !== null) {
                $questionScore = $this->getQuestionScore($question, $answer, $scoring);
                $totalScore += $questionScore;
                
                // Handle subscales if defined
                if (isset($question['subscale'])) {
                    $subscale = $question['subscale'];
                    $subscores[$subscale] = ($subscores[$subscale] ?? 0) + $questionScore;
                }
            }

            $maxScore += $this->getMaxQuestionScore($question, $scoring);
        }

        return [
            'total_score' => $totalScore,
            'max_score' => $maxScore,
            'percentage' => $maxScore > 0 ? round(($totalScore / $maxScore) * 100, 2) : 0,
            'subscores' => $subscores,
        ];
    }

    /**
     * Get interpretation based on score
     */
    public function getInterpretation(array $scoreData): array
    {
        $rules = $this->interpretation_rules;
        $totalScore = $scoreData['total_score'];
        $percentage = $scoreData['percentage'];

        foreach ($rules as $rule) {
            if ($this->scoreMatchesRule($totalScore, $percentage, $rule)) {
                return [
                    'level' => $rule['level'],
                    'label' => $rule['label'],
                    'description' => $rule['description'],
                    'recommendations' => $rule['recommendations'] ?? [],
                    'color' => $rule['color'] ?? 'blue',
                ];
            }
        }

        return [
            'level' => 'unknown',
            'label' => 'Unknown',
            'description' => 'Unable to determine interpretation',
            'recommendations' => [],
            'color' => 'gray',
        ];
    }

    /**
     * Get question score
     */
    private function getQuestionScore(array $question, $answer, array $scoring): int
    {
        $questionType = $question['type'];
        
        switch ($questionType) {
            case 'likert':
                return (int) $answer;
            
            case 'yes_no':
                return $answer === 'yes' ? 1 : 0;
            
            case 'multiple_choice':
                $scoreMap = $scoring['multiple_choice'] ?? [];
                return $scoreMap[$answer] ?? 0;
            
            default:
                return 0;
        }
    }

    /**
     * Get maximum question score
     */
    private function getMaxQuestionScore(array $question, array $scoring): int
    {
        $questionType = $question['type'];
        
        switch ($questionType) {
            case 'likert':
                return $scoring['likert']['max'] ?? 4;
            
            case 'yes_no':
                return 1;
            
            case 'multiple_choice':
                $scoreMap = $scoring['multiple_choice'] ?? [];
                return max(array_values($scoreMap));
            
            default:
                return 0;
        }
    }

    /**
     * Check if score matches interpretation rule
     */
    private function scoreMatchesRule(int $score, float $percentage, array $rule): bool
    {
        if (isset($rule['score_range'])) {
            $min = $rule['score_range']['min'] ?? 0;
            $max = $rule['score_range']['max'] ?? PHP_INT_MAX;
            return $score >= $min && $score <= $max;
        }

        if (isset($rule['percentage_range'])) {
            $min = $rule['percentage_range']['min'] ?? 0;
            $max = $rule['percentage_range']['max'] ?? 100;
            return $percentage >= $min && $percentage <= $max;
        }

        return false;
    }
}
