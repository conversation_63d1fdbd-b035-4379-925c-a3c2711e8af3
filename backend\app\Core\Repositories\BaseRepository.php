<?php

namespace App\Core\Repositories;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

abstract class BaseRepository
{
    protected Model $model;

    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    /**
     * Get all records
     */
    public function all(): Collection
    {
        return $this->model->all();
    }

    /**
     * Find record by ID
     */
    public function findById(string $id): ?Model
    {
        return $this->model->find($id);
    }

    /**
     * Find record by ID or fail
     */
    public function findByIdOrFail(string $id): Model
    {
        return $this->model->findOrFail($id);
    }

    /**
     * Create new record
     */
    public function create(array $data): Model
    {
        return $this->model->create($data);
    }

    /**
     * Update existing record
     */
    public function update(Model $model, array $data): Model
    {
        $model->update($data);
        return $model->fresh();
    }

    /**
     * Delete record
     */
    public function delete(Model $model): bool
    {
        return $model->delete();
    }

    /**
     * Get paginated records with search and sorting
     */
    public function getPaginated(
        ?string $search = null,
        int $perPage = 15,
        string $sortBy = 'created_at',
        string $sortOrder = 'desc'
    ): LengthAwarePaginator {
        $query = $this->model->newQuery();

        if ($search) {
            $query = $this->applySearch($query, $search);
        }

        return $query->orderBy($sortBy, $sortOrder)
                    ->paginate($perPage);
    }

    /**
     * Get active records
     */
    public function getActive(): Collection
    {
        return $this->model->where('is_active', true)->get();
    }

    /**
     * Search records
     */
    public function search(string $term): Collection
    {
        $query = $this->model->newQuery();
        return $this->applySearch($query, $term)->get();
    }

    /**
     * Apply search filters to query
     * Override this method in child repositories for custom search logic
     */
    protected function applySearch(Builder $query, string $search): Builder
    {
        // Default search implementation
        // Override in child classes for specific search logic
        return $query->where('name', 'like', "%{$search}%");
    }

    /**
     * Get records by field value
     */
    public function getByField(string $field, $value): Collection
    {
        return $this->model->where($field, $value)->get();
    }

    /**
     * Get first record by field value
     */
    public function getFirstByField(string $field, $value): ?Model
    {
        return $this->model->where($field, $value)->first();
    }

    /**
     * Count records
     */
    public function count(): int
    {
        return $this->model->count();
    }

    /**
     * Check if record exists
     */
    public function exists(string $id): bool
    {
        return $this->model->where('id', $id)->exists();
    }

    /**
     * Get records with relationships
     */
    public function with(array $relations): self
    {
        $this->model = $this->model->with($relations);
        return $this;
    }

    /**
     * Apply where conditions
     */
    public function where(string $field, $operator, $value = null): self
    {
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }

        $this->model = $this->model->where($field, $operator, $value);
        return $this;
    }

    /**
     * Apply order by
     */
    public function orderBy(string $field, string $direction = 'asc'): self
    {
        $this->model = $this->model->orderBy($field, $direction);
        return $this;
    }

    /**
     * Get fresh model instance
     */
    protected function getFreshModel(): Model
    {
        return $this->model->newInstance();
    }
}
