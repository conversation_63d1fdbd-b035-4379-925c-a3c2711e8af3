<IfModule mod_rewrite.c>
    RewriteEngine On

    # Handle requests for existing files and directories in root
    RewriteCond %{REQUEST_FILENAME} -f [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^ - [L]

    # Skip redirect for specific files
    RewriteCond %{REQUEST_URI} !^/santrimental.conf$
    RewriteCond %{REQUEST_URI} !^/update-hosts.bat$
    RewriteCond %{REQUEST_URI} !^/create-symlink.bat$
    RewriteCond %{REQUEST_URI} !^/test.html$

    # Internal rewrite (no redirect) to Lara<PERSON> backend/public
    RewriteCond %{REQUEST_URI} !^/backend/public/
    RewriteRule ^(.*)$ /backend/public/$1 [L,QSA]
</IfModule>
