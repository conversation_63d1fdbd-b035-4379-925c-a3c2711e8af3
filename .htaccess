<IfModule mod_rewrite.c>
    RewriteEngine On

    # Security: Block access to sensitive files and directories
    RewriteRule ^backend/\.env$ - [F,L]
    RewriteRule ^backend/storage/ - [F,L]
    RewriteRule ^backend/bootstrap/cache/ - [F,L]
    RewriteRule ^backend/vendor/ - [F,L]
    RewriteRule ^\.git/ - [F,L]

    # Handle static files in root (DOCS, frontend assets, etc.)
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule ^.*$ - [L]

    # Handle directories in root (allow browsing DOCS, frontend, etc.)
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteCond %{REQUEST_URI} ^/(DOCS|frontend|docs|assets)/
    RewriteRule ^.*$ - [L]

    # Redirect everything else to Laravel backend/public
    RewriteCond %{REQUEST_URI} !^/backend/public/
    RewriteRule ^(.*)$ /backend/public/$1 [L,QSA]
</IfModule>
