<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\FormTemplate;
use App\Models\FormResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class FormTemplateController extends Controller
{
    /**
     * Get all available form templates
     */
    public function index()
    {
        $forms = FormTemplate::where('is_active', true)
            ->select('id', 'code', 'name', 'description', 'category', 'time_limit')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $forms
        ]);
    }

    /**
     * Get specific form template with questions
     */
    public function show($code)
    {
        $form = FormTemplate::where('code', $code)
            ->where('is_active', true)
            ->first();

        if (!$form) {
            return response()->json([
                'success' => false,
                'message' => 'Form template not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $form
        ]);
    }

    /**
     * Submit form response
     */
    public function submitResponse(Request $request, $code)
    {
        $user = Auth::user();
        $user = \App\Models\User::with('role')->find($user->id);

        // Check if user can take assessments (only siswa)
        if (!$user->isSiswa()) {
            return response()->json([
                'success' => false,
                'message' => 'Only students can take assessments'
            ], 403);
        }

        $form = FormTemplate::where('code', $code)
            ->where('is_active', true)
            ->first();

        if (!$form) {
            return response()->json([
                'success' => false,
                'message' => 'Form template not found'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'answers' => 'required|array',
            'completion_time' => 'nullable|integer'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Calculate score
        $answers = $request->answers;
        $totalScore = $form->calculateScore($answers);

        // Get interpretation
        $interpretation = $form->interpretScore($totalScore);

        // Save response
        $response = FormResponse::create([
            'user_id' => Auth::id(),
            'form_template_id' => $form->id,
            'answers' => $answers,
            'total_score' => $totalScore,
            'status' => $interpretation['status'],
            'interpretation' => $interpretation['interpretation'],
            'recommendations' => json_encode($interpretation['recommendations']),
            'completion_time' => $request->completion_time
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $response->id,
                'total_score' => $totalScore,
                'status' => $interpretation['status'],
                'interpretation' => $interpretation['interpretation'],
                'recommendations' => $interpretation['recommendations']
            ]
        ]);
    }

    /**
     * Get user's form responses
     */
    public function getUserResponses($code = null)
    {
        $query = FormResponse::with('formTemplate')
            ->where('user_id', Auth::id());

        if ($code) {
            $form = FormTemplate::where('code', $code)->first();
            if ($form) {
                $query->where('form_template_id', $form->id);
            }
        }

        $responses = $query->orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => $responses
        ]);
    }

    /**
     * Get dashboard statistics for all forms
     */
    public function getDashboardStats()
    {
        $userId = Auth::id();

        $totalResponses = FormResponse::where('user_id', $userId)->count();

        $latestResponse = FormResponse::with('formTemplate')
            ->where('user_id', $userId)
            ->latest()
            ->first();

        $monthlyStats = FormResponse::where('user_id', $userId)
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->get();

        $trend = FormResponse::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get()
            ->map(function ($response) {
                return [
                    'date' => $response->created_at->format('Y-m-d'),
                    'score' => $response->total_score,
                    'form_name' => $response->formTemplate->name
                ];
            });

        return response()->json([
            'success' => true,
            'data' => [
                'total_assessments' => $totalResponses,
                'latest_assessment' => $latestResponse ? [
                    'total_score' => $latestResponse->total_score,
                    'status' => $latestResponse->status,
                    'form_name' => $latestResponse->formTemplate->name,
                    'created_at' => $latestResponse->created_at
                ] : null,
                'monthly_stats' => [
                    'total_assessments' => $monthlyStats->count(),
                    'average_score' => $monthlyStats->avg('total_score'),
                    'concerns' => $monthlyStats->where('status', '!=', 'normal')->count()
                ],
                'trend' => $trend
            ]
        ]);
    }
}
