<?php

namespace App\Modules\Assessment\Models;

use App\Core\Traits\HasUuidPrimaryKey;
use App\Core\Traits\HasTimestamps;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * AssessmentResponse Model
 * 
 * @property string $id
 * @property int $user_id
 * @property string $form_id
 * @property array $answers
 * @property array $score_data
 * @property array $interpretation
 * @property string $status
 * @property int $completion_time
 * @property \Carbon\Carbon $started_at
 * @property \Carbon\Carbon $completed_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class AssessmentResponse extends Model
{
    use HasFactory, HasUuidPrimaryKey, HasTimestamps;

    /**
     * The table associated with the model.
     */
    protected $table = 'assessment_responses';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'form_id',
        'answers',
        'score_data',
        'interpretation',
        'status',
        'completion_time',
        'started_at',
        'completed_at',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'answers' => 'array',
        'score_data' => 'array',
        'interpretation' => 'array',
        'completion_time' => 'integer',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Response statuses
     */
    public const STATUS_STARTED = 'started';
    public const STATUS_IN_PROGRESS = 'in_progress';
    public const STATUS_COMPLETED = 'completed';
    public const STATUS_ABANDONED = 'abandoned';

    public const STATUSES = [
        self::STATUS_STARTED => 'Started',
        self::STATUS_IN_PROGRESS => 'In Progress',
        self::STATUS_COMPLETED => 'Completed',
        self::STATUS_ABANDONED => 'Abandoned',
    ];

    /**
     * Validation rules for the model.
     */
    public static function rules(): array
    {
        return [
            'user_id' => 'required|integer|exists:users,id',
            'form_id' => 'required|uuid|exists:assessment_forms,id',
            'answers' => 'nullable|array',
            'score_data' => 'nullable|array',
            'interpretation' => 'nullable|array',
            'status' => 'required|string|in:' . implode(',', array_keys(self::STATUSES)),
            'completion_time' => 'nullable|integer|min:0',
            'started_at' => 'nullable|date',
            'completed_at' => 'nullable|date|after:started_at',
        ];
    }

    /**
     * Get the user that owns the response
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the assessment form
     */
    public function form(): BelongsTo
    {
        return $this->belongsTo(AssessmentForm::class, 'form_id');
    }

    /**
     * Scope to filter by status
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter completed responses
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope to filter by user
     */
    public function scopeByUser($query, string $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by form
     */
    public function scopeByForm($query, string $formId)
    {
        return $query->where('form_id', $formId);
    }

    /**
     * Scope to filter by date range
     */
    public function scopeDateRange($query, $from = null, $to = null)
    {
        if ($from) {
            $query->where('completed_at', '>=', $from);
        }

        if ($to) {
            $query->where('completed_at', '<=', $to);
        }

        return $query;
    }

    /**
     * Check if response is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if response is in progress
     */
    public function isInProgress(): bool
    {
        return in_array($this->status, [self::STATUS_STARTED, self::STATUS_IN_PROGRESS]);
    }

    /**
     * Mark response as started
     */
    public function markAsStarted(): bool
    {
        return $this->update([
            'status' => self::STATUS_STARTED,
            'started_at' => now(),
        ]);
    }

    /**
     * Mark response as in progress
     */
    public function markAsInProgress(): bool
    {
        return $this->update([
            'status' => self::STATUS_IN_PROGRESS,
        ]);
    }

    /**
     * Mark response as completed
     */
    public function markAsCompleted(array $answers, array $scoreData, array $interpretation): bool
    {
        $completedAt = now();
        $completionTime = $this->started_at 
            ? $completedAt->diffInSeconds($this->started_at)
            : null;

        return $this->update([
            'status' => self::STATUS_COMPLETED,
            'answers' => $answers,
            'score_data' => $scoreData,
            'interpretation' => $interpretation,
            'completed_at' => $completedAt,
            'completion_time' => $completionTime,
        ]);
    }

    /**
     * Mark response as abandoned
     */
    public function markAsAbandoned(): bool
    {
        return $this->update([
            'status' => self::STATUS_ABANDONED,
        ]);
    }

    /**
     * Get total score
     */
    public function getTotalScoreAttribute(): ?int
    {
        return $this->score_data['total_score'] ?? null;
    }

    /**
     * Get score percentage
     */
    public function getScorePercentageAttribute(): ?float
    {
        return $this->score_data['percentage'] ?? null;
    }

    /**
     * Get interpretation level
     */
    public function getInterpretationLevelAttribute(): ?string
    {
        return $this->interpretation['level'] ?? null;
    }

    /**
     * Get interpretation label
     */
    public function getInterpretationLabelAttribute(): ?string
    {
        return $this->interpretation['label'] ?? null;
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? 'Unknown';
    }

    /**
     * Get completion time in human readable format
     */
    public function getCompletionTimeHumanAttribute(): ?string
    {
        if (!$this->completion_time) {
            return null;
        }

        $minutes = floor($this->completion_time / 60);
        $seconds = $this->completion_time % 60;

        if ($minutes > 0) {
            return "{$minutes}m {$seconds}s";
        }

        return "{$seconds}s";
    }

    /**
     * Get progress percentage
     */
    public function getProgressPercentageAttribute(): float
    {
        if (!$this->answers || !$this->form) {
            return 0;
        }

        $totalQuestions = count($this->form->questions ?? []);
        $answeredQuestions = count(array_filter($this->answers));

        return $totalQuestions > 0 ? round(($answeredQuestions / $totalQuestions) * 100, 2) : 0;
    }
}
